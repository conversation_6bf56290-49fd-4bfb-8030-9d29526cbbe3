/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  output: 'export',
  // Disable asset prefixing in Next.js - we'll handle this with a custom script
  // This prevents double-prefixing issues
  assetPrefix: '',
  // Ensure consistent file hashing across builds
  generateBuildId: async () => {
    // Use a fixed build ID for consistent file names
    return 'stable-build'
  },
  images: {
    unoptimized: true, // Required for static export
    deviceSizes: [640, 750, 828, 1080, 1200, 1920],
    imageSizes: [16, 32, 48, 64, 96, 128, 256],
    formats: ['image/webp', 'image/avif'], // Support AVIF for better compression
    minimumCacheTTL: 31536000, // Cache for 1 year
    dangerouslyAllowSVG: true, // Allow SVG for icons
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  // Disable trailing slashes in URLs
  trailingSlash: false,
  // Disable the automatic static optimization
  experimental: {
    // Enable if you need to use rewrites or redirects
    // skipTrailingSlashRedirect: true,
    // Enable CSS optimization with safeguards
    optimizeCss: true,
  },
  // Ensure consistent output
  webpack: (config, { isServer }) => {
    // Use deterministic chunk and module ids for consistent file names
    config.optimization.moduleIds = 'deterministic';
    config.optimization.chunkIds = 'deterministic';

    // Optimize Lottie animations
    config.module.rules.push({
      test: /\.json$/,
      include: /animations/,
      type: 'asset/resource',
      generator: {
        filename: 'static/animations/[name].[hash][ext]'
      }
    });

    // Split chunks for better caching
    if (!isServer) {
      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks?.cacheGroups,
          lottie: {
            test: /[\\/]node_modules[\\/](lottie-react|lottie-web)[\\/]/,
            name: 'lottie',
            chunks: 'all',
            priority: 10,
          },
          animations: {
            test: /[\\/]animations[\\/].*\.json$/,
            name: 'animations',
            chunks: 'all',
            priority: 5,
          }
        }
      };
    }

    return config;
  },
}

module.exports = nextConfig
