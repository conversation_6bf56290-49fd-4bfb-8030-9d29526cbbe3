const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Lottie animation optimization script
class LottieOptimizer {
  constructor() {
    this.animationsDir = path.join(__dirname, '../src/animations');
    this.optimizedDir = path.join(__dirname, '../src/animations/optimized');
    this.compressionStats = [];
  }

  // Main optimization function
  async optimize() {
    console.log('🎬 Starting Lottie animation optimization...');
    
    // Ensure optimized directory exists
    if (!fs.existsSync(this.optimizedDir)) {
      fs.mkdirSync(this.optimizedDir, { recursive: true });
    }

    // Find all JSON animation files
    const animationFiles = glob.sync(path.join(this.animationsDir, '*.json'));
    
    console.log(`Found ${animationFiles.length} animation files to optimize`);

    for (const filePath of animationFiles) {
      await this.optimizeFile(filePath);
    }

    this.printSummary();
    await this.generateManifest();
  }

  // Optimize individual file
  async optimizeFile(filePath) {
    const fileName = path.basename(filePath);
    const originalSize = fs.statSync(filePath).size;
    
    console.log(`\n📁 Processing: ${fileName} (${this.formatBytes(originalSize)})`);

    try {
      // Read and parse animation data
      const animationData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      
      // Apply optimizations
      const optimized = this.optimizeAnimationData(animationData);
      
      // Create different quality versions
      const versions = {
        high: optimized,
        medium: this.createMediumQuality(optimized),
        low: this.createLowQuality(optimized)
      };

      // Save optimized versions
      for (const [quality, data] of Object.entries(versions)) {
        const outputPath = path.join(
          this.optimizedDir, 
          `${path.parse(fileName).name}-${quality}.json`
        );
        
        const optimizedJson = JSON.stringify(data);
        fs.writeFileSync(outputPath, optimizedJson);
        
        const optimizedSize = Buffer.byteLength(optimizedJson);
        const reduction = ((originalSize - optimizedSize) / originalSize) * 100;
        
        console.log(`  ✅ ${quality}: ${this.formatBytes(optimizedSize)} (${reduction.toFixed(1)}% reduction)`);
        
        this.compressionStats.push({
          fileName,
          quality,
          originalSize,
          optimizedSize,
          reduction
        });
      }

    } catch (error) {
      console.error(`❌ Error processing ${fileName}:`, error.message);
    }
  }

  // Core optimization logic
  optimizeAnimationData(data) {
    const optimized = JSON.parse(JSON.stringify(data)); // Deep clone

    // Remove unnecessary metadata
    delete optimized.meta;
    delete optimized.markers;
    
    // Optimize layers
    if (optimized.layers) {
      optimized.layers = optimized.layers.map(layer => this.optimizeLayer(layer));
    }

    // Optimize assets
    if (optimized.assets) {
      optimized.assets = optimized.assets.filter(asset => asset.id); // Remove empty assets
    }

    // Round numeric values to reduce precision
    this.roundNumericValues(optimized);

    return optimized;
  }

  // Optimize individual layer
  optimizeLayer(layer) {
    const optimized = { ...layer };

    // Remove hidden layers
    if (optimized.hd === true) {
      return null;
    }

    // Simplify shapes
    if (optimized.shapes) {
      optimized.shapes = optimized.shapes.map(shape => this.optimizeShape(shape));
    }

    // Optimize keyframes
    if (optimized.ks) {
      this.optimizeKeyframes(optimized.ks);
    }

    return optimized;
  }

  // Optimize shape data
  optimizeShape(shape) {
    const optimized = { ...shape };

    // Round path data
    if (optimized.ks && optimized.ks.k) {
      this.roundPathData(optimized.ks.k);
    }

    return optimized;
  }

  // Optimize keyframe data
  optimizeKeyframes(ks) {
    if (ks.k && Array.isArray(ks.k)) {
      ks.k = ks.k.map(keyframe => {
        if (typeof keyframe === 'object' && keyframe.s) {
          // Round keyframe values
          keyframe.s = keyframe.s.map(val => 
            typeof val === 'number' ? Math.round(val * 100) / 100 : val
          );
        }
        return keyframe;
      });
    }
  }

  // Round path data for smaller file size
  roundPathData(pathData) {
    if (Array.isArray(pathData)) {
      pathData.forEach(point => {
        if (Array.isArray(point)) {
          for (let i = 0; i < point.length; i++) {
            if (typeof point[i] === 'number') {
              point[i] = Math.round(point[i] * 10) / 10;
            }
          }
        }
      });
    }
  }

  // Round all numeric values in the animation
  roundNumericValues(obj, precision = 2) {
    for (const key in obj) {
      if (typeof obj[key] === 'number') {
        obj[key] = Math.round(obj[key] * Math.pow(10, precision)) / Math.pow(10, precision);
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        this.roundNumericValues(obj[key], precision);
      }
    }
  }

  // Create medium quality version
  createMediumQuality(data) {
    const medium = JSON.parse(JSON.stringify(data));
    
    // Reduce frame rate
    if (medium.fr) {
      medium.fr = Math.min(medium.fr, 30);
    }

    // Simplify complex animations
    this.simplifyComplexAnimations(medium);

    return medium;
  }

  // Create low quality version
  createLowQuality(data) {
    const low = JSON.parse(JSON.stringify(data));
    
    // Significantly reduce frame rate
    if (low.fr) {
      low.fr = Math.min(low.fr, 15);
    }

    // Remove non-essential layers
    if (low.layers) {
      low.layers = low.layers.filter(layer => 
        !layer.nm || !layer.nm.includes('shadow') && !layer.nm.includes('glow')
      );
    }

    // Simplify animations more aggressively
    this.simplifyComplexAnimations(low, true);

    return low;
  }

  // Simplify complex animations
  simplifyComplexAnimations(data, aggressive = false) {
    if (data.layers) {
      data.layers.forEach(layer => {
        if (layer.shapes) {
          layer.shapes = layer.shapes.filter(shape => {
            // Remove complex effects in aggressive mode
            if (aggressive && shape.ty === 'gf') return false; // Remove gradients
            if (aggressive && shape.ty === 'gs') return false; // Remove gradient strokes
            return true;
          });
        }
      });
    }
  }

  // Generate manifest file for dynamic loading
  async generateManifest() {
    const manifest = {
      animations: {},
      generated: new Date().toISOString(),
      totalSavings: 0
    };

    // Group stats by animation
    const animationGroups = {};
    this.compressionStats.forEach(stat => {
      if (!animationGroups[stat.fileName]) {
        animationGroups[stat.fileName] = {};
      }
      animationGroups[stat.fileName][stat.quality] = {
        size: stat.optimizedSize,
        reduction: stat.reduction
      };
    });

    // Build manifest
    for (const [fileName, qualities] of Object.entries(animationGroups)) {
      const baseName = path.parse(fileName).name;
      manifest.animations[baseName] = {
        original: fileName,
        qualities,
        recommended: this.getRecommendedQuality(qualities)
      };
    }

    // Calculate total savings
    const totalOriginal = this.compressionStats.reduce((sum, stat) => 
      stat.quality === 'high' ? sum + stat.originalSize : sum, 0
    );
    const totalOptimized = this.compressionStats.reduce((sum, stat) => 
      stat.quality === 'medium' ? sum + stat.optimizedSize : sum, 0
    );
    
    manifest.totalSavings = ((totalOriginal - totalOptimized) / totalOriginal) * 100;

    // Save manifest
    const manifestPath = path.join(this.optimizedDir, 'manifest.json');
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
    
    console.log(`\n📋 Generated manifest: ${manifestPath}`);
  }

  // Get recommended quality based on file size
  getRecommendedQuality(qualities) {
    const mediumSize = qualities.medium?.size || 0;
    
    if (mediumSize > 1024 * 1024) { // > 1MB
      return 'low';
    } else if (mediumSize > 512 * 1024) { // > 512KB
      return 'medium';
    } else {
      return 'high';
    }
  }

  // Print optimization summary
  printSummary() {
    console.log('\n🎯 Optimization Summary:');
    console.log('========================');
    
    const totalOriginal = this.compressionStats.reduce((sum, stat) => 
      stat.quality === 'high' ? sum + stat.originalSize : sum, 0
    );
    const totalOptimized = this.compressionStats.reduce((sum, stat) => 
      stat.quality === 'medium' ? sum + stat.optimizedSize : sum, 0
    );
    
    const totalReduction = ((totalOriginal - totalOptimized) / totalOriginal) * 100;
    
    console.log(`📊 Total original size: ${this.formatBytes(totalOriginal)}`);
    console.log(`📊 Total optimized size: ${this.formatBytes(totalOptimized)}`);
    console.log(`📊 Total reduction: ${totalReduction.toFixed(1)}%`);
    console.log(`📊 Files processed: ${this.compressionStats.length / 3} animations`);
  }

  // Format bytes for display
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Run optimization if called directly
if (require.main === module) {
  const optimizer = new LottieOptimizer();
  optimizer.optimize().catch(console.error);
}

module.exports = LottieOptimizer;
