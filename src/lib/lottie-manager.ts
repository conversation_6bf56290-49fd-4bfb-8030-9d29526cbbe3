// Lottie Animation Manager for performance optimization
import { prefersReducedMotion } from './motion'

interface LottieConfig {
  autoplay?: boolean
  loop?: boolean
  speed?: number
  quality?: 'low' | 'medium' | 'high'
  preload?: boolean
  lazy?: boolean
}

interface AnimationMetrics {
  loadTime: number
  renderTime: number
  fileSize: number
  memoryUsage?: number
}

class LottieAnimationManager {
  private cache = new Map<string, any>()
  private loadingPromises = new Map<string, Promise<any>>()
  private observers = new Map<string, IntersectionObserver>()
  private animationInstances = new Map<string, any>()
  private metrics = new Map<string, AnimationMetrics>()
  private preloadQueue: string[] = []

  private getCacheKey(animationKey: string, config: LottieConfig = {}): string {
    return config.quality ? `${animationKey}::${config.quality}` : animationKey
  }

  // Animation file mappings for dynamic imports
  private animationPaths = {
    'notification': 'Notification-[remix].json',
    'showreel': 'Showreel_-Web-gallery-[remix] (2).json',
    'list-4-3': 'List-(4_3).json',
    'list-9-16': 'List-(9_16).json',
    'hero-bg': 'hero-bg.json',
    'masthead-pattern': 'masthead-pattern-animation.json'
  }

  // Lazy load animation data with performance tracking
  async loadAnimation(animationKey: string, config: LottieConfig = {}): Promise<any> {
    const startTime = performance.now()
    const cacheKey = this.getCacheKey(animationKey, config)
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      const loadTime = performance.now() - startTime
      this.updateMetrics(animationKey, { loadTime })
      return this.cache.get(cacheKey)
    }

    // Check if already loading
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey)
    }

    // Start loading with performance tracking
    const loadPromise = this.fetchAnimation(animationKey, config)
    this.loadingPromises.set(cacheKey, loadPromise)

    try {
      const animationData = await loadPromise
      const loadTime = performance.now() - startTime
      
      // Store in cache
      this.cache.set(cacheKey, animationData)
      this.loadingPromises.delete(cacheKey)
      
      // Track metrics
      this.updateMetrics(animationKey, { 
        loadTime,
        fileSize: JSON.stringify(animationData).length 
      })
      
      return animationData
    } catch (error) {
      this.loadingPromises.delete(cacheKey)
      console.error(`Failed to load animation: ${animationKey}`, error)
      throw error
    }
  }

  private async fetchAnimation(animationKey: string, config: LottieConfig): Promise<any> {
    const fileName = this.animationPaths[animationKey as keyof typeof this.animationPaths]
    if (!fileName) {
      throw new Error(`Animation key "${animationKey}" not found`)
    }

    try {
      // Dynamic import for code splitting
      const animationModule: { default?: unknown } | string = await import(`@/animations/${fileName}`)
      let animationData: any = (animationModule as any)?.default ?? animationModule

      // When webpack treats animations as `asset/resource`, the import resolves to a URL string
      if (typeof animationData === 'string') {
        const response = await fetch(animationData)
        if (!response.ok) {
          throw new Error(`Failed to fetch animation data for "${animationKey}"`)
        }
        animationData = await response.json()
      }

      if (!animationData || typeof animationData !== 'object' || !('layers' in animationData)) {
        throw new Error(`Animation data for "${animationKey}" is invalid or missing layer information`)
      }

      // Apply quality optimizations
      if (config.quality === 'low') {
        animationData = this.optimizeForLowQuality(animationData)
      }

      return animationData
    } catch (error) {
      console.error(`Failed to fetch animation: ${fileName}`, error)
      throw error
    }
  }

  // Optimize animation for low quality/performance
  private optimizeForLowQuality(animationData: any): any {
    if (!animationData || typeof animationData !== 'object') {
      return animationData
    }

    const optimized = { ...animationData }

    // Reduce frame rate for better performance
    if (typeof optimized.fr === 'number') {
      optimized.fr = Math.min(optimized.fr, 30)
    }

    // Simplify complex paths if needed
    // This is a basic optimization - more sophisticated ones could be added
    return optimized
  }

  // Register animation instance for cleanup and monitoring
  registerInstance(id: string, instance: any, config: LottieConfig = {}): void {
    this.animationInstances.set(id, { instance, config })
    
    // Apply reduced motion preferences
    if (prefersReducedMotion() && instance) {
      if (typeof instance.setSpeed === 'function') {
        instance.setSpeed(0.1) // Very slow animation
      }
      if (typeof instance.pause === 'function') {
        instance.pause() // Or pause entirely
      }
    }
  }

  // Cleanup animation instance and free memory
  cleanup(id: string): void {
    const instanceData = this.animationInstances.get(id)
    if (instanceData?.instance) {
      const { instance } = instanceData
      
      // Destroy Lottie instance
      if (typeof instance.destroy === 'function') {
        instance.destroy()
      }
      
      this.animationInstances.delete(id)
    }

    // Cleanup intersection observer
    const observer = this.observers.get(id)
    if (observer) {
      observer.disconnect()
      this.observers.delete(id)
    }
  }

  // Create intersection observer for lazy loading
  createIntersectionObserver(
    id: string,
    callback: () => void,
    options: IntersectionObserverInit = {}
  ): IntersectionObserver {
    const defaultOptions = {
      rootMargin: '100px', // Load 100px before entering viewport
      threshold: 0.1,
      ...options
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          callback()
          observer.disconnect()
          this.observers.delete(id)
        }
      })
    }, defaultOptions)

    this.observers.set(id, observer)
    return observer
  }

  // Preload critical animations during idle time
  async preloadCriticalAnimations(animationKeys: string[]): Promise<void> {
    // Use requestIdleCallback if available
    if ('requestIdleCallback' in window) {
      return new Promise((resolve) => {
        window.requestIdleCallback(async () => {
          await this.batchLoadAnimations(animationKeys)
          resolve()
        })
      })
    } else {
      // Fallback to setTimeout
      return new Promise((resolve) => {
        setTimeout(async () => {
          await this.batchLoadAnimations(animationKeys)
          resolve()
        }, 100)
      })
    }
  }

  private async batchLoadAnimations(animationKeys: string[]): Promise<void> {
    const promises = animationKeys.map(key => 
      this.loadAnimation(key, { preload: true }).catch(err => {
        console.warn(`Failed to preload animation: ${key}`, err)
        return null
      })
    )
    await Promise.all(promises)
  }

  // Update performance metrics
  private updateMetrics(animationKey: string, metrics: Partial<AnimationMetrics>): void {
    const existing = this.metrics.get(animationKey) || {
      loadTime: 0,
      renderTime: 0,
      fileSize: 0
    }
    
    this.metrics.set(animationKey, { ...existing, ...metrics })
  }

  // Get performance metrics for monitoring
  getMetrics(animationKey?: string): AnimationMetrics | Map<string, AnimationMetrics> {
    if (animationKey) {
      return this.metrics.get(animationKey) || {
        loadTime: 0,
        renderTime: 0,
        fileSize: 0
      }
    }
    return this.metrics
  }

  // Get cache statistics
  getCacheStats(): { size: number; keys: string[]; totalSize: number } {
    let totalSize = 0
    const keys = Array.from(this.cache.keys())
    
    keys.forEach(key => {
      const data = this.cache.get(key)
      if (data) {
        totalSize += JSON.stringify(data).length
      }
    })

    return {
      size: this.cache.size,
      keys,
      totalSize
    }
  }

  // Clear cache and cleanup all instances
  clearAll(): void {
    // Cleanup all instances
    this.animationInstances.forEach((_, id) => this.cleanup(id))
    
    // Clear caches
    this.cache.clear()
    this.loadingPromises.clear()
    this.metrics.clear()
    
    // Disconnect all observers
    this.observers.forEach(observer => observer.disconnect())
    this.observers.clear()
  }

  // Memory management - remove least recently used items
  manageCacheSize(maxItems: number = 10): void {
    if (this.cache.size <= maxItems) return

    const keys = Array.from(this.cache.keys())
    const itemsToRemove = keys.slice(0, keys.length - maxItems)
    
    itemsToRemove.forEach(key => {
      this.cache.delete(key)
      this.metrics.delete(key)
    })
  }
}

// Export singleton instance
export const lottieManager = new LottieAnimationManager()

// Performance monitoring utilities
export const performanceMonitor = {
  measureAnimationLoad: async (name: string, loadFn: () => Promise<any>) => {
    const start = performance.now()
    try {
      const result = await loadFn()
      const end = performance.now()
      console.log(`🎬 Animation ${name} loaded in ${(end - start).toFixed(2)}ms`)
      return result
    } catch (error) {
      const end = performance.now()
      console.error(`❌ Animation ${name} failed to load after ${(end - start).toFixed(2)}ms`, error)
      throw error
    }
  },

  measureRenderTime: (name: string, renderFn: () => void) => {
    const start = performance.now()
    renderFn()
    const end = performance.now()
    console.log(`🎨 Animation ${name} rendered in ${(end - start).toFixed(2)}ms`)
  },

  logCacheStats: () => {
    const stats = lottieManager.getCacheStats()
    console.log(`📊 Lottie Cache: ${stats.size} items, ${(stats.totalSize / 1024).toFixed(2)}KB total`)
  }
}

export default lottieManager
