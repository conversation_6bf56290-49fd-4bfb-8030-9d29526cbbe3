import { useEffect, useRef, useState, useCallback } from 'react'

interface UseIntersectionObserverOptions extends IntersectionObserverInit {
  freezeOnceVisible?: boolean
  triggerOnce?: boolean
}

interface UseIntersectionObserverReturn {
  ref: React.RefObject<Element>
  isIntersecting: boolean
  entry: IntersectionObserverEntry | null
}

/**
 * Custom hook for intersection observer with performance optimizations
 */
export const useIntersectionObserver = (
  options: UseIntersectionObserverOptions = {}
): UseIntersectionObserverReturn => {
  const {
    threshold = 0.1,
    root = null,
    rootMargin = '50px',
    freezeOnceVisible = false,
    triggerOnce = false,
    ...restOptions
  } = options

  const elementRef = useRef<Element>(null)
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null)
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasTriggered, setHasTriggered] = useState(false)

  const frozen = entry?.isIntersecting && freezeOnceVisible

  const updateEntry = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries
      
      if (triggerOnce && hasTriggered && !entry.isIntersecting) {
        return // Don't update if triggerOnce is true and we've already triggered
      }

      setEntry(entry)
      setIsIntersecting(entry.isIntersecting)
      
      if (entry.isIntersecting && triggerOnce) {
        setHasTriggered(true)
      }
    },
    [triggerOnce, hasTriggered]
  )

  useEffect(() => {
    const element = elementRef.current
    const hasIOSupport = !!window.IntersectionObserver

    if (!hasIOSupport || frozen || !element) {
      return
    }

    const observerParams = {
      threshold,
      root,
      rootMargin,
      ...restOptions
    }

    const observer = new IntersectionObserver(updateEntry, observerParams)
    observer.observe(element)

    return () => {
      observer.disconnect()
    }
  }, [
    elementRef.current,
    JSON.stringify(threshold),
    root,
    rootMargin,
    frozen,
    updateEntry,
    restOptions
  ])

  return {
    ref: elementRef,
    isIntersecting,
    entry
  }
}

/**
 * Hook specifically optimized for lazy loading animations
 */
export const useLazyAnimation = (options: {
  preloadDistance?: string
  triggerOnce?: boolean
  onVisible?: () => void
  onHidden?: () => void
} = {}) => {
  const {
    preloadDistance = '100px',
    triggerOnce = true,
    onVisible,
    onHidden
  } = options

  const { ref, isIntersecting, entry } = useIntersectionObserver({
    rootMargin: preloadDistance,
    threshold: 0.1,
    triggerOnce
  })

  const [shouldLoad, setShouldLoad] = useState(false)
  const [hasLoaded, setHasLoaded] = useState(false)

  useEffect(() => {
    if (isIntersecting && !shouldLoad) {
      setShouldLoad(true)
      onVisible?.()
    } else if (!isIntersecting && shouldLoad && !triggerOnce) {
      onHidden?.()
    }
  }, [isIntersecting, shouldLoad, triggerOnce, onVisible, onHidden])

  const markAsLoaded = useCallback(() => {
    setHasLoaded(true)
  }, [])

  return {
    ref,
    shouldLoad,
    hasLoaded,
    isIntersecting,
    markAsLoaded,
    entry
  }
}

/**
 * Hook for managing multiple animations with staggered loading
 */
export const useStaggeredAnimations = (
  count: number,
  options: {
    staggerDelay?: number
    preloadDistance?: string
  } = {}
) => {
  const { staggerDelay = 100, preloadDistance = '50px' } = options
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set())
  const [loadedItems, setLoadedItems] = useState<Set<number>>(new Set())
  
  const refs = useRef<(Element | null)[]>(Array(count).fill(null))
  const timeouts = useRef<NodeJS.Timeout[]>([])

  useEffect(() => {
    const observers: IntersectionObserver[] = []

    refs.current.forEach((element, index) => {
      if (!element) return

      const observer = new IntersectionObserver(
        (entries) => {
          const [entry] = entries
          if (entry.isIntersecting) {
            // Stagger the visibility
            const timeout = setTimeout(() => {
              setVisibleItems(prev => new Set([...prev, index]))
              
              // Mark as loaded after animation delay
              setTimeout(() => {
                setLoadedItems(prev => new Set([...prev, index]))
              }, 500)
            }, index * staggerDelay)
            
            timeouts.current[index] = timeout
            observer.disconnect()
          }
        },
        {
          rootMargin: preloadDistance,
          threshold: 0.1
        }
      )

      observer.observe(element)
      observers.push(observer)
    })

    return () => {
      observers.forEach(observer => observer.disconnect())
      timeouts.current.forEach(timeout => clearTimeout(timeout))
    }
  }, [count, staggerDelay, preloadDistance])

  const setRef = useCallback((index: number) => (element: Element | null) => {
    refs.current[index] = element
  }, [])

  const isVisible = useCallback((index: number) => visibleItems.has(index), [visibleItems])
  const isLoaded = useCallback((index: number) => loadedItems.has(index), [loadedItems])

  return {
    setRef,
    isVisible,
    isLoaded,
    visibleCount: visibleItems.size,
    loadedCount: loadedItems.size
  }
}

/**
 * Hook for performance-aware animation loading
 */
export const usePerformantAnimation = (options: {
  priority?: 'high' | 'medium' | 'low'
  maxConcurrent?: number
  respectReducedMotion?: boolean
} = {}) => {
  const {
    priority = 'medium',
    maxConcurrent = 3,
    respectReducedMotion = true
  } = options

  const [canLoad, setCanLoad] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const loadingCount = useRef(0)

  // Check for reduced motion preference
  const prefersReducedMotion = useCallback(() => {
    if (!respectReducedMotion || typeof window === 'undefined') return false
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  }, [respectReducedMotion])

  // Check device performance
  const isLowEndDevice = useCallback(() => {
    if (typeof navigator === 'undefined') return false
    
    // Check for low-end device indicators
    const connection = (navigator as any).connection
    if (connection) {
      return connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g'
    }
    
    // Check hardware concurrency
    return navigator.hardwareConcurrency <= 2
  }, [])

  const { ref, isIntersecting } = useIntersectionObserver({
    rootMargin: priority === 'high' ? '200px' : priority === 'medium' ? '100px' : '50px',
    threshold: 0.1
  })

  useEffect(() => {
    if (!isIntersecting) return

    // Don't load if reduced motion is preferred and we respect it
    if (prefersReducedMotion()) {
      setCanLoad(false)
      return
    }

    // Check if we can load based on concurrent animations
    if (loadingCount.current >= maxConcurrent) {
      // Queue for later
      const checkInterval = setInterval(() => {
        if (loadingCount.current < maxConcurrent) {
          setCanLoad(true)
          clearInterval(checkInterval)
        }
      }, 100)

      return () => clearInterval(checkInterval)
    }

    setCanLoad(true)
  }, [isIntersecting, maxConcurrent, prefersReducedMotion])

  const startLoading = useCallback(() => {
    if (canLoad && !isLoading) {
      setIsLoading(true)
      loadingCount.current++
    }
  }, [canLoad, isLoading])

  const finishLoading = useCallback(() => {
    if (isLoading) {
      setIsLoading(false)
      loadingCount.current = Math.max(0, loadingCount.current - 1)
    }
  }, [isLoading])

  return {
    ref,
    canLoad,
    isLoading,
    startLoading,
    finishLoading,
    shouldUseReducedMotion: prefersReducedMotion(),
    isLowEndDevice: isLowEndDevice()
  }
}

export default useIntersectionObserver
