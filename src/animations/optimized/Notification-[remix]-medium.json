{"assets": [{"h": 135, "id": "0", "p": "data:image/png;base64,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", "u": "", "w": 132, "e": 1}, {"id": "10", "layers": [{"ind": 7, "ty": 2, "parent": 6, "ks": {"p": {"a": 0, "k": [0, 4]}, "s": {"a": 0, "k": [31.82, 31.85]}}, "ip": 0, "op": 91, "st": 0, "refId": "0"}, {"ind": 6, "ty": 3, "ks": {"p": {"a": 0, "k": [42, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 9, "ty": 2, "parent": 8, "ks": {"p": {"a": 0, "k": [0, 4]}, "s": {"a": 0, "k": [31.82, 31.85]}}, "ip": 0, "op": 91, "st": 0, "refId": "0"}, {"ind": 8, "ty": 3, "ks": {}, "ip": 0, "op": 91, "st": 0}]}, {"id": "30", "layers": [{"ind": 15, "ty": 4, "parent": 14, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, -0.6], [0, -0.6], [0.1, -0.6], [0.1, -0.7], [0, 0], [0, 0], [0.1, 0.6], [0, 0.6], [0, 0.6], [0, 0.6], [0, 0], [0, 0]], "v": [[5.5, 11.4], [9, 11.4], [9, 23.4], [9, 25.2], [8.9, 27], [8.7, 28.9], [8.5, 31], [8.5, 31], [6, 31], [5.8, 28.9], [5.6, 27], [5.5, 25.2], [5.5, 23.4], [5.5, 23.4], [5.5, 11.4]], "o": [[0, 0], [0, 0], [0, 0.6], [0, 0.6], [0, 0.6], [-0.1, 0.6], [0, 0], [0, 0], [-0.1, -0.7], [-0.1, -0.6], [0, -0.6], [0, -0.6], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.1, 0.3], [-0.2, 0.2], [-0.3, 0.1], [-0.4, 0], [0, 0], [-0.3, -0.1], [-0.2, -0.2], [-0.1, -0.3], [0, -0.4], [0, 0], [0.1, -0.3], [0.2, -0.2], [0.3, -0.1], [0.4, 0], [0, 0], [0.3, 0.1], [0.2, 0.2], [0.1, 0.3], [0, 0.4]], "v": [[4.6, 39.1], [4.6, 39.1], [4.8, 38.1], [5.3, 37.3], [6.2, 36.7], [7.2, 36.5], [7.2, 36.5], [8.2, 36.7], [9.1, 37.3], [9.6, 38.1], [9.8, 39.1], [9.8, 39.1], [9.6, 40.2], [9.1, 41], [8.2, 41.6], [7.2, 41.8], [7.2, 41.8], [6.2, 41.6], [5.3, 41], [4.8, 40.2], [4.6, 39.1]], "o": [[0, 0], [0, -0.4], [0.1, -0.3], [0.2, -0.2], [0.3, -0.1], [0, 0], [0.4, 0], [0.3, 0.1], [0.2, 0.2], [0.1, 0.3], [0, 0], [0, 0.4], [-0.1, 0.3], [-0.2, 0.2], [-0.3, 0.1], [0, 0], [-0.4, 0], [-0.3, -0.1], [-0.2, -0.2], [-0.1, -0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 14, "ty": 3, "parent": 13, "ks": {"p": {"a": 0, "k": [131.42, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 17, "ty": 4, "parent": 16, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.9, 0.9], [0, 1.8], [0, 0], [0, 0], [0, 0], [0.2, 0.1], [0, 0.3], [0, 0], [0, 0], [0, 0], [0, 0], [-0.2, 0.1], [-0.2, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.4, -0.4], [-0.7, 0], [0, 0], [-0.3, 0.1], [-0.2, 0.1], [-0.1, 0.1], [-0.1, 0], [0, 0], [-0.2, -0.2], [0, 0], [0, 0], [0.9, -0.3], [1, 0]], "v": [[9.5, 41.8], [9.5, 41.8], [5.6, 40.4], [4.3, 36.3], [4.3, 36.3], [4.3, 23.3], [1.7, 23.3], [1.2, 23.1], [0.9, 22.5], [0.9, 22.5], [0.9, 21], [4.4, 20.6], [5.3, 14], [5.5, 13.5], [6.1, 13.3], [6.1, 13.3], [8, 13.3], [8, 20.6], [14.1, 20.6], [14.1, 23.3], [8, 23.3], [8, 36.1], [8.7, 38.1], [10.4, 38.7], [10.4, 38.7], [11.4, 38.6], [12.1, 38.2], [12.7, 37.9], [13, 37.7], [13, 37.7], [13.6, 38.1], [13.6, 38.1], [14.7, 39.9], [12.3, 41.3], [9.5, 41.8]], "o": [[0, 0], [-1.7, 0], [-0.9, -0.9], [0, 0], [0, 0], [0, 0], [-0.2, 0], [-0.2, -0.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.2], [0.2, -0.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.9], [0.4, 0.4], [0, 0], [0.4, 0], [0.3, -0.1], [0.2, -0.1], [0.1, -0.1], [0, 0], [0.2, 0], [0, 0], [0, 0], [-0.6, 0.6], [-0.9, 0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 16, "ty": 3, "parent": 13, "ks": {"p": {"a": 0, "k": [115.75, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 19, "ty": 4, "parent": 18, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.1, -0.1], [0.2, 0], [0, 0], [0.2, 0.2], [0.4, 0.2], [0.5, 0.2], [0.8, 0], [0, 0], [0.8, -0.4], [0.5, -0.7], [0.3, -1], [0, -1.2], [0, 0], [-0.3, -1], [-0.5, -0.7], [-0.8, -0.4], [-1, 0], [0, 0], [-0.6, 0.2], [-0.4, 0.3], [-0.3, 0.2], [-0.3, 0], [0, 0], [-0.2, -0.2], [0, 0], [0, 0], [1.4, -0.5], [1.5, 0], [0, 0], [1.1, 0.5], [0.8, 0.9], [0.5, 1.4], [0, 1.7], [0, 0], [-0.4, 1.3], [-0.8, 1], [-1.2, 0.5], [-1.6, 0], [0, 0], [-1.1, -0.5], [-0.9, -0.9], [0, 0]], "v": [[18.6, 22.6], [17.6, 24], [17.2, 24.3], [16.8, 24.4], [16.8, 24.4], [16.1, 24.2], [15.1, 23.6], [13.8, 23], [11.8, 22.8], [11.8, 22.8], [9, 23.3], [7, 24.9], [5.8, 27.5], [5.4, 30.8], [5.4, 30.8], [5.8, 34.2], [7.1, 36.8], [9, 38.3], [11.6, 38.9], [11.6, 38.9], [13.8, 38.5], [15.3, 37.8], [16.3, 37.1], [17, 36.8], [17, 36.8], [17.7, 37.1], [17.7, 37.1], [18.8, 38.5], [15.3, 41], [10.9, 41.7], [10.9, 41.7], [7.2, 41], [4.3, 38.9], [2.3, 35.4], [1.6, 30.8], [1.6, 30.8], [2.2, 26.4], [4.1, 22.9], [7.3, 20.7], [11.6, 19.8], [11.6, 19.8], [15.5, 20.6], [18.6, 22.6], [18.6, 22.6]], "o": [[0, 0], [-0.1, 0.2], [-0.1, 0.1], [0, 0], [-0.2, 0], [-0.2, -0.2], [-0.4, -0.2], [-0.5, -0.2], [0, 0], [-1, 0], [-0.8, 0.4], [-0.5, 0.7], [-0.3, 1], [0, 0], [0, 1.3], [0.3, 1], [0.5, 0.7], [0.8, 0.4], [0, 0], [0.9, 0], [0.6, -0.2], [0.4, -0.3], [0.3, -0.2], [0, 0], [0.3, 0], [0, 0], [0, 0], [-0.9, 1.1], [-1.4, 0.5], [0, 0], [-1.3, 0], [-1.1, -0.5], [-0.8, -0.9], [-0.5, -1.4], [0, 0], [0, -1.6], [0.4, -1.3], [0.8, -1], [1.2, -0.5], [0, 0], [1.5, 0], [1.1, 0.5], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 18, "ty": 3, "parent": 13, "ks": {"p": {"a": 0, "k": [96.14, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 21, "ty": 4, "parent": 20, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-1.1, -0.4], [-0.8, -0.8], [-0.4, -1.2], [0, -1.5], [0, 0], [0.1, -0.2], [0.4, 0], [0, 0], [0, 0], [-0.3, -1], [-0.6, -0.7], [-0.8, -0.3], [-1, 0], [0, 0], [-0.7, 0.2], [-0.5, 0.3], [-0.3, 0.2], [-0.2, 0], [0, 0], [-0.2, -0.2], [0, 0], [0, 0], [0.6, -0.4], [0.7, -0.3], [0.8, -0.1], [0.8, 0], [0, 0], [1.2, 0.5], [0.9, 1], [0.5, 1.4], [0, 1.8], [0, 0], [-0.5, 1.3], [-0.9, 0.9], [-1.2, 0.5], [-1.5, 0]], "v": [[11.5, 19.8], [11.5, 19.8], [15, 20.5], [17.8, 22.3], [19.6, 25.3], [20.3, 29.4], [20.3, 29.4], [20.1, 30.5], [19.4, 30.8], [19.4, 30.8], [5.3, 30.8], [5.8, 34.3], [7.2, 36.8], [9.3, 38.3], [12, 38.8], [12, 38.8], [14.4, 38.5], [16.2, 37.8], [17.4, 37.1], [18.3, 36.8], [18.3, 36.8], [19, 37.1], [19, 37.1], [20, 38.5], [18.4, 39.9], [16.3, 41], [14, 41.5], [11.7, 41.7], [11.7, 41.7], [7.6, 41], [4.4, 38.8], [2.3, 35.3], [1.6, 30.4], [1.6, 30.4], [2.2, 26.2], [4.2, 22.9], [7.3, 20.7], [11.5, 19.8]], "o": [[0, 0], [1.3, 0], [1.1, 0.4], [0.8, 0.8], [0.4, 1.2], [0, 0], [0, 0.6], [-0.1, 0.2], [0, 0], [0, 0], [0, 1.3], [0.3, 1], [0.6, 0.7], [0.8, 0.3], [0, 0], [0.9, 0], [0.7, -0.2], [0.5, -0.3], [0.3, -0.2], [0, 0], [0.3, 0], [0, 0], [0, 0], [-0.5, 0.6], [-0.6, 0.4], [-0.7, 0.3], [-0.8, 0.1], [0, 0], [-1.5, 0], [-1.2, -0.5], [-0.9, -1], [-0.5, -1.4], [0, 0], [0, -1.5], [0.5, -1.3], [0.9, -0.9], [1.2, -0.5], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1, -1], [0.3, -1.8], [0, 0], [0, 0], [0.2, 0.7], [0.5, 0.5], [0.7, 0.3], [0.9, 0]], "v": [[11.6, 22.6], [11.6, 22.6], [7.3, 24.2], [5.4, 28.5], [5.4, 28.5], [16.9, 28.5], [16.6, 26.1], [15.5, 24.2], [13.8, 23], [11.6, 22.6]], "o": [[0, 0], [-1.8, 0], [-1, 1], [0, 0], [0, 0], [0, -0.9], [-0.2, -0.7], [-0.5, -0.5], [-0.7, -0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 20, "ty": 3, "parent": 13, "ks": {"p": {"a": 0, "k": [74.13, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 23, "ty": 4, "parent": 22, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.2, -0.7], [0.5, -0.5], [0.8, -0.3], [1.1, 0], [0, 0], [0.4, 0.1], [0.4, 0.1], [0, 0], [0, 0], [-0.1, 0], [-0.2, 0], [0, 0], [-0.1, 0], [-0.2, 0], [0, 0], [-0.5, 0.5], [0, 1.1], [0, 0], [0, 0]], "v": [[3.5, 20.2], [7.2, 20.2], [7.2, 43], [6.9, 45.4], [5.8, 47.3], [4, 48.6], [1.2, 49.1], [1.2, 49.1], [0, 49], [-1.2, 48.7], [-1.2, 48.7], [-1, 46.7], [-0.8, 46.3], [-0.4, 46.2], [-0.4, 46.2], [0, 46.3], [0.5, 46.3], [0.5, 46.3], [2.8, 45.5], [3.5, 43], [3.5, 43], [3.5, 20.2]], "o": [[0, 0], [0, 0], [0, 0.9], [-0.2, 0.7], [-0.5, 0.5], [-0.8, 0.3], [0, 0], [-0.5, 0], [-0.4, -0.1], [0, 0], [0, 0], [0, -0.2], [0.1, 0], [0, 0], [0.1, 0], [0.1, 0], [0, 0], [1.1, 0], [0.5, -0.5], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.1, -0.3], [0.2, -0.2], [0.3, -0.1], [0.4, 0], [0, 0], [0.3, 0.1], [0.2, 0.2], [0.1, 0.3], [0, 0.4], [0, 0], [-0.1, 0.3], [-0.2, 0.2], [-0.3, 0.1], [-0.4, 0], [0, 0], [-0.3, -0.1], [-0.2, -0.2], [-0.1, -0.3], [0, -0.4]], "v": [[8.1, 13.5], [8.1, 13.5], [7.8, 14.5], [7.3, 15.4], [6.4, 15.9], [5.4, 16.1], [5.4, 16.1], [4.4, 15.9], [3.5, 15.4], [2.9, 14.5], [2.7, 13.5], [2.7, 13.5], [2.9, 12.5], [3.5, 11.6], [4.4, 11], [5.4, 10.8], [5.4, 10.8], [6.4, 11], [7.3, 11.6], [7.8, 12.5], [8.1, 13.5]], "o": [[0, 0], [0, 0.4], [-0.1, 0.3], [-0.2, 0.2], [-0.3, 0.1], [0, 0], [-0.4, 0], [-0.3, -0.1], [-0.2, -0.2], [-0.1, -0.3], [0, 0], [0, -0.4], [0.1, -0.3], [0.2, -0.2], [0.3, -0.1], [0, 0], [0.4, 0], [0.3, 0.1], [0.2, 0.2], [0.1, 0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 22, "ty": 3, "parent": 13, "ks": {"p": {"a": 0, "k": [63.46, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 25, "ty": 4, "parent": 24, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-1.3, -0.5], [-0.9, -1], [-0.5, -1.4], [0, -1.7], [0, 0], [0.5, -1.3], [0.9, -1], [1.3, -0.5], [1.6, 0], [0, 0], [1.3, 0.5], [0.9, 1], [0.5, 1.3], [0, 1.7], [0, 0], [-0.5, 1.4], [-0.9, 1], [-1.3, 0.5], [-1.6, 0]], "v": [[11.7, 19.8], [11.7, 19.8], [15.9, 20.6], [19.1, 22.8], [21.1, 26.3], [21.8, 30.8], [21.8, 30.8], [21.1, 35.3], [19.1, 38.8], [15.9, 41], [11.7, 41.7], [11.7, 41.7], [7.5, 41], [4.3, 38.8], [2.2, 35.3], [1.5, 30.8], [1.5, 30.8], [2.2, 26.3], [4.3, 22.8], [7.5, 20.6], [11.7, 19.8]], "o": [[0, 0], [1.6, 0], [1.3, 0.5], [0.9, 1], [0.5, 1.4], [0, 0], [0, 1.7], [-0.5, 1.3], [-0.9, 1], [-1.3, 0.5], [0, 0], [-1.6, 0], [-1.3, -0.5], [-0.9, -1], [-0.5, -1.3], [0, 0], [0, -1.7], [0.5, -1.4], [0.9, -1], [1.3, -0.5], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-1, 1.4], [0, 2.5], [0, 0], [1, 1.4], [2.1, 0], [0, 0], [0.8, -0.4], [0.5, -0.7], [0.3, -1], [0, -1.3], [0, 0], [-0.3, -1], [-0.5, -0.7], [-0.8, -0.4], [-1.1, 0]], "v": [[11.7, 38.8], [11.7, 38.8], [16.4, 36.7], [17.9, 30.8], [17.9, 30.8], [16.4, 24.9], [11.7, 22.8], [11.7, 22.8], [8.9, 23.3], [6.9, 24.9], [5.8, 27.4], [5.4, 30.8], [5.4, 30.8], [5.8, 34.2], [6.9, 36.7], [8.9, 38.3], [11.7, 38.8]], "o": [[0, 0], [2.1, 0], [1, -1.4], [0, 0], [0, -2.5], [-1, -1.4], [0, 0], [-1.1, 0], [-0.8, 0.4], [-0.5, 0.7], [-0.3, 1], [0, 0], [0, 1.3], [0.3, 1], [0.5, 0.7], [0.8, 0.4], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 24, "ty": 3, "parent": 13, "ks": {"p": {"a": 0, "k": [40.11, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 27, "ty": 4, "parent": 26, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.2, -0.2], [-0.1, -0.4], [0, 0], [0, 0], [-1.1, 0.8], [-1.4, 0], [0, 0], [-0.5, -0.1], [-0.4, -0.2], [0, 0], [0, 0], [0.3, 0], [0, 0], [0.4, 0.1], [0.7, 0], [0, 0], [0.9, -0.8], [0.6, -1.4], [0, 0], [0, 0]], "v": [[6.8, 41.5], [3.1, 41.5], [3.1, 20.2], [5.2, 20.2], [6, 20.4], [6.4, 21.2], [6.4, 21.2], [6.6, 24.5], [9.3, 21.1], [13.1, 19.8], [13.1, 19.8], [14.7, 20], [16, 20.6], [16, 20.6], [15.5, 23.4], [14.9, 23.9], [14.9, 23.9], [14, 23.7], [12.3, 23.5], [12.3, 23.5], [9, 24.6], [6.8, 27.9], [6.8, 27.9], [6.8, 41.5]], "o": [[0, 0], [0, 0], [0, 0], [0.4, 0], [0.2, 0.2], [0, 0], [0, 0], [0.7, -1.5], [1.1, -0.8], [0, 0], [0.6, 0], [0.5, 0.1], [0, 0], [0, 0], [-0.1, 0.3], [0, 0], [-0.2, 0], [-0.4, -0.1], [0, 0], [-1.3, 0], [-0.9, 0.8], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 26, "ty": 3, "parent": 13, "ks": {"p": {"a": 0, "k": [23.18, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 29, "ty": 4, "parent": 28, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.1, -0.5], [0, 0], [0, 0], [-1.2, 0.7], [-1.5, 0], [0, 0], [-1, -0.5], [-0.7, -0.9], [-0.4, -1.4], [0, -1.8], [0, 0], [0.4, -1.4], [0.8, -1], [1.1, -0.6], [1.4, 0], [0, 0], [0.9, 0.4], [0.7, 0.8], [0, 0], [0, 0]], "v": [[6.8, 48.7], [3.1, 48.7], [3.1, 20.2], [5.3, 20.2], [6.3, 21], [6.3, 21], [6.6, 23.5], [9.7, 20.8], [13.8, 19.8], [13.8, 19.8], [17.1, 20.5], [19.6, 22.6], [21.3, 26], [21.8, 30.7], [21.8, 30.7], [21.2, 35.1], [19.4, 38.6], [16.5, 40.9], [12.6, 41.7], [12.6, 41.7], [9.3, 41.1], [6.8, 39.2], [6.8, 39.2], [6.8, 48.7]], "o": [[0, 0], [0, 0], [0, 0], [0.5, 0], [0, 0], [0, 0], [0.9, -1.1], [1.2, -0.7], [0, 0], [1.2, 0], [1, 0.5], [0.7, 0.9], [0.4, 1.4], [0, 0], [0, 1.6], [-0.4, 1.4], [-0.8, 1], [-1.1, 0.6], [0, 0], [-1.3, 0], [-0.9, -0.4], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.9, -0.6], [0.8, -1], [0, 0], [0, 0], [-0.8, -0.4], [-1, 0], [0, 0], [-1.1, 1.4], [0, 2.6], [0, 0], [0.2, 1], [0.5, 0.6], [0.7, 0.3], [0.9, 0]], "v": [[12.5, 22.8], [12.5, 22.8], [9.3, 23.7], [6.8, 26], [6.8, 26], [6.8, 36.3], [9.1, 38.3], [11.8, 38.9], [11.8, 38.9], [16.4, 36.7], [18, 30.7], [18, 30.7], [17.6, 27.1], [16.5, 24.7], [14.8, 23.3], [12.5, 22.8]], "o": [[0, 0], [-1.2, 0], [-0.9, 0.6], [0, 0], [0, 0], [0.7, 0.9], [0.8, 0.4], [0, 0], [2, 0], [1.1, -1.4], [0, 0], [0, -1.4], [-0.2, -1], [-0.5, -0.6], [-0.7, -0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 28, "ty": 3, "parent": 13, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 13, "ty": 3, "ks": {"p": {"a": 0, "k": [2, -10]}}, "ip": 0, "op": 91, "st": 0}]}, {"id": "42", "layers": [{"ind": 35, "ty": 4, "parent": 34, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.2, -0.2], [-0.1, -0.4], [0, 0], [0, 0], [-1.1, 0.8], [-1.4, 0], [0, 0], [-0.5, -0.1], [-0.4, -0.2], [0, 0], [0, 0], [0.3, 0], [0, 0], [0.4, 0.1], [0.7, 0], [0, 0], [0.9, -0.8], [0.6, -1.4], [0, 0], [0, 0]], "v": [[6.8, 41.5], [3.1, 41.5], [3.1, 20.2], [5.2, 20.2], [6, 20.4], [6.4, 21.2], [6.4, 21.2], [6.6, 24.5], [9.3, 21.1], [13.1, 19.8], [13.1, 19.8], [14.7, 20], [16, 20.6], [16, 20.6], [15.5, 23.4], [14.9, 23.9], [14.9, 23.9], [14, 23.7], [12.3, 23.5], [12.3, 23.5], [9, 24.6], [6.8, 27.9], [6.8, 27.9], [6.8, 41.5]], "o": [[0, 0], [0, 0], [0, 0], [0.4, 0], [0.2, 0.2], [0, 0], [0, 0], [0.7, -1.5], [1.1, -0.8], [0, 0], [0.6, 0], [0.5, 0.1], [0, 0], [0, 0], [-0.1, 0.3], [0, 0], [-0.2, 0], [-0.4, -0.1], [0, 0], [-1.3, 0], [-0.9, 0.8], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 34, "ty": 3, "parent": 33, "ks": {"p": {"a": 0, "k": [67.66, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 37, "ty": 4, "parent": 36, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [-0.7, -0.9], [-1.5, 0], [0, 0], [-1, 0.5], [-0.8, 0.9], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.1, 0.5], [0, 0], [0, 0], [1.1, -0.6], [1.5, 0], [0, 0], [0.9, 0.4], [0.6, 0.7], [0.3, 1], [0, 1.2], [0, 0], [0, 0]], "v": [[2.6, 20.2], [6.3, 20.2], [6.3, 33.7], [7.4, 37.5], [10.8, 38.8], [10.8, 38.8], [13.9, 38], [16.5, 35.9], [16.5, 35.9], [16.5, 20.2], [20.3, 20.2], [20.3, 41.5], [18, 41.5], [17, 40.7], [17, 40.7], [16.7, 38.4], [13.6, 40.9], [9.7, 41.8], [9.7, 41.8], [6.6, 41.2], [4.4, 39.6], [3, 37], [2.6, 33.7], [2.6, 33.7], [2.6, 20.2]], "o": [[0, 0], [0, 0], [0, 1.6], [0.7, 0.9], [0, 0], [1.1, 0], [1, -0.5], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.5, 0], [0, 0], [0, 0], [-0.9, 1], [-1.1, 0.6], [0, 0], [-1.2, 0], [-0.9, -0.4], [-0.6, -0.7], [-0.3, -1], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 36, "ty": 3, "parent": 33, "ks": {"p": {"a": 0, "k": [44.31, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 39, "ty": 4, "parent": 38, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-1.3, -0.5], [-0.9, -1], [-0.5, -1.4], [0, -1.7], [0, 0], [0.5, -1.3], [0.9, -1], [1.3, -0.5], [1.6, 0], [0, 0], [1.3, 0.5], [0.9, 1], [0.5, 1.3], [0, 1.7], [0, 0], [-0.5, 1.4], [-0.9, 1], [-1.3, 0.5], [-1.6, 0]], "v": [[11.7, 19.8], [11.7, 19.8], [15.9, 20.6], [19.1, 22.8], [21.1, 26.3], [21.8, 30.8], [21.8, 30.8], [21.1, 35.3], [19.1, 38.8], [15.9, 41], [11.7, 41.7], [11.7, 41.7], [7.5, 41], [4.3, 38.8], [2.2, 35.3], [1.5, 30.8], [1.5, 30.8], [2.2, 26.3], [4.3, 22.8], [7.5, 20.6], [11.7, 19.8]], "o": [[0, 0], [1.6, 0], [1.3, 0.5], [0.9, 1], [0.5, 1.4], [0, 0], [0, 1.7], [-0.5, 1.3], [-0.9, 1], [-1.3, 0.5], [0, 0], [-1.6, 0], [-1.3, -0.5], [-0.9, -1], [-0.5, -1.3], [0, 0], [0, -1.7], [0.5, -1.4], [0.9, -1], [1.3, -0.5], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-1, 1.4], [0, 2.5], [0, 0], [1, 1.4], [2.1, 0], [0, 0], [0.8, -0.4], [0.5, -0.7], [0.3, -1], [0, -1.3], [0, 0], [-0.3, -1], [-0.5, -0.7], [-0.8, -0.4], [-1.1, 0]], "v": [[11.7, 38.8], [11.7, 38.8], [16.4, 36.7], [17.9, 30.8], [17.9, 30.8], [16.4, 24.9], [11.7, 22.8], [11.7, 22.8], [8.9, 23.3], [6.9, 24.9], [5.8, 27.4], [5.4, 30.8], [5.4, 30.8], [5.8, 34.2], [6.9, 36.7], [8.9, 38.3], [11.7, 38.8]], "o": [[0, 0], [2.1, 0], [1, -1.4], [0, 0], [0, -2.5], [-1, -1.4], [0, 0], [-1.1, 0], [-0.8, 0.4], [-0.5, 0.7], [-0.3, 1], [0, 0], [0, 1.3], [0.3, 1], [0.5, 0.7], [0.8, 0.4], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 38, "ty": 3, "parent": 33, "ks": {"p": {"a": 0, "k": [20.96, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 41, "ty": 4, "parent": 40, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.2, -0.2], [0.4, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.2, -0.2], [-0.1, -0.2], [0, 0], [0, 0], [-0.1, -0.3], [-0.1, -0.3], [0, 0], [-0.1, 0.3], [-0.1, 0.3], [0, 0], [0, 0], [-0.2, 0.1], [-0.2, 0], [0, 0], [0, 0]], "v": [[21.2, 20.2], [9.3, 47.7], [8.8, 48.4], [8, 48.7], [8, 48.7], [5.2, 48.7], [9.1, 40.2], [0.3, 20.2], [3.5, 20.2], [4.3, 20.4], [4.7, 21], [4.7, 21], [10.4, 34.4], [10.7, 35.3], [10.9, 36.2], [10.9, 36.2], [11.2, 35.3], [11.6, 34.3], [11.6, 34.3], [17.1, 21], [17.5, 20.4], [18.2, 20.2], [18.2, 20.2], [21.2, 20.2]], "o": [[0, 0], [-0.1, 0.3], [-0.2, 0.2], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.3, 0], [0.2, 0.2], [0, 0], [0, 0], [0.1, 0.3], [0.1, 0.3], [0, 0], [0.1, -0.3], [0.1, -0.3], [0, 0], [0, 0], [0.1, -0.2], [0.2, -0.1], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 40, "ty": 3, "parent": 33, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 33, "ty": 3, "ks": {"p": {"a": 0, "k": [0, -19]}}, "ip": 0, "op": 91, "st": 0}]}, {"id": "56", "layers": [{"ind": 47, "ty": 4, "parent": 46, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.9, 0.9], [0, 1.8], [0, 0], [0, 0], [0, 0], [0.2, 0.1], [0, 0.3], [0, 0], [0, 0], [0, 0], [0, 0], [-0.2, 0.1], [-0.2, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.4, -0.4], [-0.7, 0], [0, 0], [-0.3, 0.1], [-0.2, 0.1], [-0.1, 0.1], [-0.1, 0], [0, 0], [-0.2, -0.2], [0, 0], [0, 0], [0.9, -0.3], [1, 0]], "v": [[9.5, 41.8], [9.5, 41.8], [5.6, 40.4], [4.3, 36.3], [4.3, 36.3], [4.3, 23.3], [1.7, 23.3], [1.2, 23.1], [0.9, 22.5], [0.9, 22.5], [0.9, 21], [4.4, 20.6], [5.3, 14], [5.5, 13.5], [6.1, 13.3], [6.1, 13.3], [8, 13.3], [8, 20.6], [14.1, 20.6], [14.1, 23.3], [8, 23.3], [8, 36.1], [8.7, 38.1], [10.4, 38.7], [10.4, 38.7], [11.4, 38.6], [12.1, 38.2], [12.7, 37.9], [13, 37.7], [13, 37.7], [13.6, 38.1], [13.6, 38.1], [14.7, 39.9], [12.3, 41.3], [9.5, 41.8]], "o": [[0, 0], [-1.7, 0], [-0.9, -0.9], [0, 0], [0, 0], [0, 0], [-0.2, 0], [-0.2, -0.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.2], [0.2, -0.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.9], [0.4, 0.4], [0, 0], [0.4, 0], [0.3, -0.1], [0.2, -0.1], [0.1, -0.1], [0, 0], [0.2, 0], [0, 0], [0, 0], [-0.6, 0.6], [-0.9, 0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 46, "ty": 3, "parent": 45, "ks": {"p": {"a": 0, "k": [72.11, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 49, "ty": 4, "parent": 48, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.2, -0.2], [-0.1, -0.4], [0, 0], [0, 0], [-1.1, 0.8], [-1.4, 0], [0, 0], [-0.5, -0.1], [-0.4, -0.2], [0, 0], [0, 0], [0.3, 0], [0, 0], [0.4, 0.1], [0.7, 0], [0, 0], [0.9, -0.8], [0.6, -1.4], [0, 0], [0, 0]], "v": [[6.8, 41.5], [3.1, 41.5], [3.1, 20.2], [5.2, 20.2], [6, 20.4], [6.4, 21.2], [6.4, 21.2], [6.6, 24.5], [9.3, 21.1], [13.1, 19.8], [13.1, 19.8], [14.7, 20], [16, 20.6], [16, 20.6], [15.5, 23.4], [14.9, 23.9], [14.9, 23.9], [14, 23.7], [12.3, 23.5], [12.3, 23.5], [9, 24.6], [6.8, 27.9], [6.8, 27.9], [6.8, 41.5]], "o": [[0, 0], [0, 0], [0, 0], [0.4, 0], [0.2, 0.2], [0, 0], [0, 0], [0.7, -1.5], [1.1, -0.8], [0, 0], [0.6, 0], [0.5, 0.1], [0, 0], [0, 0], [-0.1, 0.3], [0, 0], [-0.2, 0], [-0.4, -0.1], [0, 0], [-1.3, 0], [-0.9, 0.8], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 48, "ty": 3, "parent": 45, "ks": {"p": {"a": 0, "k": [55.19, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 51, "ty": 4, "parent": 50, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.2, 0.1], [0.1, 0.4], [0, 0], [0, 0], [0.5, -0.4], [0.6, -0.3], [0.7, -0.1], [0.8, 0], [0, 0], [0.7, 0.2], [0.5, 0.5], [0.3, 0.7], [0, 1], [0, 0], [-0.5, 0.8], [-1, 0.6], [-1.7, 0.4], [-2.4, 0.1], [0, 0], [0, 0], [0.7, 0.8], [1.4, 0], [0, 0], [0.6, -0.2], [0.5, -0.3], [0.3, -0.2], [0.3, 0], [0, 0], [0.2, 0.1], [0.1, 0.2], [0, 0], [0, 0], [-1.4, 0.6], [-1.7, 0], [0, 0], [-0.9, -0.4], [-0.6, -0.7], [-0.3, -1], [0, -1.2], [0, 0]], "v": [[18.7, 27.8], [18.7, 41.5], [17, 41.5], [16.1, 41.3], [15.7, 40.6], [15.7, 40.6], [15.3, 38.6], [13.7, 40], [12, 41], [10.1, 41.6], [7.9, 41.8], [7.9, 41.8], [5.6, 41.4], [3.7, 40.4], [2.4, 38.7], [1.9, 36.1], [1.9, 36.1], [2.6, 33.7], [4.9, 31.6], [8.9, 30.2], [15, 29.5], [15, 29.5], [15, 27.8], [14, 24.1], [10.8, 22.8], [10.8, 22.8], [8.5, 23.2], [6.9, 24], [5.7, 24.7], [4.7, 25.1], [4.7, 25.1], [4.1, 24.9], [3.6, 24.4], [3.6, 24.4], [3, 23.2], [6.8, 20.6], [11.3, 19.8], [11.3, 19.8], [14.4, 20.4], [16.8, 22], [18.2, 24.6], [18.7, 27.8], [18.7, 27.8]], "o": [[0, 0], [0, 0], [-0.4, 0], [-0.2, -0.1], [0, 0], [0, 0], [-0.6, 0.5], [-0.5, 0.4], [-0.6, 0.3], [-0.7, 0.1], [0, 0], [-0.8, 0], [-0.7, -0.2], [-0.5, -0.5], [-0.3, -0.7], [0, 0], [0, -0.8], [0.5, -0.8], [1, -0.6], [1.7, -0.4], [0, 0], [0, 0], [0, -1.7], [-0.7, -0.8], [0, 0], [-0.9, 0], [-0.6, 0.2], [-0.5, 0.3], [-0.3, 0.2], [0, 0], [-0.3, 0], [-0.2, -0.1], [0, 0], [0, 0], [1.2, -1.1], [1.4, -0.6], [0, 0], [1.2, 0], [0.9, 0.4], [0.6, 0.7], [0.3, 1], [0, 0], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.5, 0.1], [-0.5, 0.2], [-0.4, 0.3], [-0.4, 0.4], [0, 0], [0, 0], [1.2, -0.2], [0.8, -0.3], [0.3, -0.5], [0, -0.6], [0, 0], [-0.2, -0.4], [-0.3, -0.3], [-0.4, -0.1], [-0.5, 0]], "v": [[9, 39.2], [9, 39.2], [10.8, 39], [12.3, 38.4], [13.7, 37.5], [15, 36.3], [15, 36.3], [15, 31.9], [10.6, 32.3], [7.7, 33.1], [6, 34.4], [5.5, 36], [5.5, 36], [5.8, 37.4], [6.5, 38.4], [7.6, 39], [9, 39.2]], "o": [[0, 0], [0.7, 0], [0.5, -0.1], [0.5, -0.2], [0.4, -0.3], [0, 0], [0, 0], [-1.7, 0.1], [-1.2, 0.2], [-0.8, 0.3], [-0.3, 0.5], [0, 0], [0, 0.6], [0.2, 0.4], [0.3, 0.3], [0.4, 0.1], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 50, "ty": 3, "parent": 45, "ks": {"p": {"a": 0, "k": [33.89, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 53, "ty": 4, "parent": 52, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.9, 0.9], [0, 1.8], [0, 0], [0, 0], [0, 0], [0.2, 0.1], [0, 0.3], [0, 0], [0, 0], [0, 0], [0, 0], [-0.2, 0.1], [-0.2, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.4, -0.4], [-0.7, 0], [0, 0], [-0.3, 0.1], [-0.2, 0.1], [-0.1, 0.1], [-0.1, 0], [0, 0], [-0.2, -0.2], [0, 0], [0, 0], [0.9, -0.3], [1, 0]], "v": [[9.5, 41.8], [9.5, 41.8], [5.6, 40.4], [4.3, 36.3], [4.3, 36.3], [4.3, 23.3], [1.7, 23.3], [1.2, 23.1], [0.9, 22.5], [0.9, 22.5], [0.9, 21], [4.4, 20.6], [5.3, 14], [5.5, 13.5], [6.1, 13.3], [6.1, 13.3], [8, 13.3], [8, 20.6], [14.1, 20.6], [14.1, 23.3], [8, 23.3], [8, 36.1], [8.7, 38.1], [10.4, 38.7], [10.4, 38.7], [11.4, 38.6], [12.1, 38.2], [12.7, 37.9], [13, 37.7], [13, 37.7], [13.6, 38.1], [13.6, 38.1], [14.7, 39.9], [12.3, 41.3], [9.5, 41.8]], "o": [[0, 0], [-1.7, 0], [-0.9, -0.9], [0, 0], [0, 0], [0, 0], [-0.2, 0], [-0.2, -0.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.2], [0.2, -0.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.9], [0.4, 0.4], [0, 0], [0.4, 0], [0.3, -0.1], [0.2, -0.1], [0.1, -0.1], [0, 0], [0.2, 0], [0, 0], [0, 0], [-0.6, 0.6], [-0.9, 0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 52, "ty": 3, "parent": 45, "ks": {"p": {"a": 0, "k": [18.23, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 55, "ty": 4, "parent": 54, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.4, 0], [0, 0], [0.3, 0.2], [0.4, 0.2], [0.5, 0.2], [0.7, 0], [0, 0], [0.5, -0.2], [0.4, -0.3], [0.2, -0.4], [0, -0.4], [0, 0], [-0.3, -0.4], [-0.5, -0.3], [-0.7, -0.2], [-0.7, -0.2], [-0.7, -0.3], [-0.5, -0.4], [-0.3, -0.6], [0, -0.9], [0, 0], [0.4, -0.8], [0.7, -0.6], [1, -0.3], [1.3, 0], [0, 0], [1.2, 0.5], [0.8, 0.8], [0, 0], [0, 0], [-0.2, 0.1], [-0.3, 0], [0, 0], [-0.3, -0.2], [-0.4, -0.2], [-0.6, -0.2], [-0.9, 0], [0, 0], [-0.5, 0.2], [-0.4, 0.3], [-0.2, 0.4], [0, 0.5], [0, 0], [0.3, 0.4], [0.5, 0.3], [0.7, 0.2], [0.7, 0.2], [0.7, 0.3], [0.5, 0.4], [0.3, 0.6], [0, 0.9], [0, 0], [-0.3, 0.7], [-0.6, 0.6], [-0.9, 0.3], [-1.2, 0], [0, 0], [-1.1, -0.4], [-0.8, -0.8], [0, 0]], "v": [[16.1, 22.3], [15.2, 23.7], [14.5, 24.2], [14.5, 24.2], [13.8, 23.9], [12.8, 23.4], [11.4, 22.9], [9.5, 22.6], [9.5, 22.6], [7.8, 22.9], [6.5, 23.5], [5.7, 24.5], [5.4, 25.7], [5.4, 25.7], [5.9, 27.1], [7.1, 28], [8.9, 28.7], [10.9, 29.4], [13, 30.2], [14.7, 31.2], [16, 32.7], [16.4, 34.9], [16.4, 34.9], [15.9, 37.7], [14.4, 39.8], [11.8, 41.3], [8.4, 41.8], [8.4, 41.8], [4.4, 41.1], [1.3, 39.2], [1.3, 39.2], [2.2, 37.8], [2.6, 37.4], [3.2, 37.2], [3.2, 37.2], [4, 37.5], [5, 38.2], [6.5, 38.8], [8.6, 39.1], [8.6, 39.1], [10.5, 38.8], [11.9, 38.1], [12.7, 36.9], [12.9, 35.6], [12.9, 35.6], [12.5, 34.1], [11.2, 33.1], [9.4, 32.4], [7.4, 31.8], [5.4, 31], [3.6, 29.9], [2.3, 28.3], [1.9, 26], [1.9, 26], [2.4, 23.7], [3.8, 21.7], [6.2, 20.3], [9.4, 19.8], [9.4, 19.8], [13.2, 20.5], [16.1, 22.3], [16.1, 22.3]], "o": [[0, 0], [-0.2, 0.3], [0, 0], [-0.2, 0], [-0.3, -0.2], [-0.4, -0.2], [-0.5, -0.2], [0, 0], [-0.6, 0], [-0.5, 0.2], [-0.4, 0.3], [-0.2, 0.4], [0, 0], [0, 0.5], [0.3, 0.4], [0.5, 0.3], [0.7, 0.2], [0.7, 0.2], [0.7, 0.3], [0.5, 0.4], [0.3, 0.6], [0, 0], [0, 1], [-0.4, 0.8], [-0.7, 0.6], [-1, 0.3], [0, 0], [-1.5, 0], [-1.2, -0.5], [0, 0], [0, 0], [0.1, -0.2], [0.2, -0.1], [0, 0], [0.3, 0], [0.3, 0.2], [0.4, 0.2], [0.6, 0.2], [0, 0], [0.7, 0], [0.5, -0.2], [0.4, -0.3], [0.2, -0.4], [0, 0], [0, -0.6], [-0.3, -0.4], [-0.5, -0.3], [-0.7, -0.2], [-0.7, -0.2], [-0.7, -0.3], [-0.5, -0.4], [-0.3, -0.6], [0, 0], [0, -0.8], [0.3, -0.7], [0.6, -0.6], [0.9, -0.3], [0, 0], [1.4, 0], [1.1, 0.4], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 54, "ty": 3, "parent": 45, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 45, "ty": 3, "ks": {"p": {"a": 0, "k": [0, -13]}}, "ip": 0, "op": 91, "st": 0}]}, {"id": "64", "layers": [{"ind": 61, "ty": 4, "parent": 60, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-1.3, -0.5], [-0.9, -1], [-0.5, -1.4], [0, -1.7], [0, 0], [0.5, -1.3], [0.9, -1], [1.3, -0.5], [1.6, 0], [0, 0], [1.3, 0.5], [0.9, 1], [0.5, 1.3], [0, 1.7], [0, 0], [-0.5, 1.4], [-0.9, 1], [-1.3, 0.5], [-1.6, 0]], "v": [[11.7, 19.8], [11.7, 19.8], [15.9, 20.6], [19.1, 22.8], [21.1, 26.3], [21.8, 30.8], [21.8, 30.8], [21.1, 35.3], [19.1, 38.8], [15.9, 41], [11.7, 41.7], [11.7, 41.7], [7.5, 41], [4.3, 38.8], [2.2, 35.3], [1.5, 30.8], [1.5, 30.8], [2.2, 26.3], [4.3, 22.8], [7.5, 20.6], [11.7, 19.8]], "o": [[0, 0], [1.6, 0], [1.3, 0.5], [0.9, 1], [0.5, 1.4], [0, 0], [0, 1.7], [-0.5, 1.3], [-0.9, 1], [-1.3, 0.5], [0, 0], [-1.6, 0], [-1.3, -0.5], [-0.9, -1], [-0.5, -1.3], [0, 0], [0, -1.7], [0.5, -1.4], [0.9, -1], [1.3, -0.5], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-1, 1.4], [0, 2.5], [0, 0], [1, 1.4], [2.1, 0], [0, 0], [0.8, -0.4], [0.5, -0.7], [0.3, -1], [0, -1.3], [0, 0], [-0.3, -1], [-0.5, -0.7], [-0.8, -0.4], [-1.1, 0]], "v": [[11.7, 38.8], [11.7, 38.8], [16.4, 36.7], [17.9, 30.8], [17.9, 30.8], [16.4, 24.9], [11.7, 22.8], [11.7, 22.8], [8.9, 23.3], [6.9, 24.9], [5.8, 27.4], [5.4, 30.8], [5.4, 30.8], [5.8, 34.2], [6.9, 36.7], [8.9, 38.3], [11.7, 38.8]], "o": [[0, 0], [2.1, 0], [1, -1.4], [0, 0], [0, -2.5], [-1, -1.4], [0, 0], [-1.1, 0], [-0.8, 0.4], [-0.5, 0.7], [-0.3, 1], [0, 0], [0, 1.3], [0.3, 1], [0.5, 0.7], [0.8, 0.4], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 60, "ty": 3, "parent": 59, "ks": {"p": {"a": 0, "k": [15.67, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 63, "ty": 4, "parent": 62, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.9, 0.9], [0, 1.8], [0, 0], [0, 0], [0, 0], [0.2, 0.1], [0, 0.3], [0, 0], [0, 0], [0, 0], [0, 0], [-0.2, 0.1], [-0.2, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.4, -0.4], [-0.7, 0], [0, 0], [-0.3, 0.1], [-0.2, 0.1], [-0.1, 0.1], [-0.1, 0], [0, 0], [-0.2, -0.2], [0, 0], [0, 0], [0.9, -0.3], [1, 0]], "v": [[9.5, 41.8], [9.5, 41.8], [5.6, 40.4], [4.3, 36.3], [4.3, 36.3], [4.3, 23.3], [1.7, 23.3], [1.2, 23.1], [0.9, 22.5], [0.9, 22.5], [0.9, 21], [4.4, 20.6], [5.3, 14], [5.5, 13.5], [6.1, 13.3], [6.1, 13.3], [8, 13.3], [8, 20.6], [14.1, 20.6], [14.1, 23.3], [8, 23.3], [8, 36.1], [8.7, 38.1], [10.4, 38.7], [10.4, 38.7], [11.4, 38.6], [12.1, 38.2], [12.7, 37.9], [13, 37.7], [13, 37.7], [13.6, 38.1], [13.6, 38.1], [14.7, 39.9], [12.3, 41.3], [9.5, 41.8]], "o": [[0, 0], [-1.7, 0], [-0.9, -0.9], [0, 0], [0, 0], [0, 0], [-0.2, 0], [-0.2, -0.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.2], [0.2, -0.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.9], [0.4, 0.4], [0, 0], [0.4, 0], [0.3, -0.1], [0.2, -0.1], [0.1, -0.1], [0, 0], [0.2, 0], [0, 0], [0, 0], [-0.6, 0.6], [-0.9, 0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 62, "ty": 3, "parent": 59, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 59, "ty": 3, "ks": {"p": {"a": 0, "k": [0, -13]}}, "ip": 0, "op": 91, "st": 0}]}, {"id": "78", "layers": [{"ind": 69, "ty": 4, "parent": 68, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.2, -0.2], [0.4, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.2, -0.2], [-0.1, -0.2], [0, 0], [0, 0], [-0.1, -0.3], [-0.1, -0.3], [0, 0], [-0.1, 0.3], [-0.1, 0.3], [0, 0], [0, 0], [-0.2, 0.1], [-0.2, 0], [0, 0], [0, 0]], "v": [[21.2, 20.2], [9.3, 47.7], [8.8, 48.4], [8, 48.7], [8, 48.7], [5.2, 48.7], [9.1, 40.2], [0.3, 20.2], [3.5, 20.2], [4.3, 20.4], [4.7, 21], [4.7, 21], [10.4, 34.4], [10.7, 35.3], [10.9, 36.2], [10.9, 36.2], [11.2, 35.3], [11.6, 34.3], [11.6, 34.3], [17.1, 21], [17.5, 20.4], [18.2, 20.2], [18.2, 20.2], [21.2, 20.2]], "o": [[0, 0], [-0.1, 0.3], [-0.2, 0.2], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.3, 0], [0.2, 0.2], [0, 0], [0, 0], [0.1, 0.3], [0.1, 0.3], [0, 0], [0.1, -0.3], [0.1, -0.3], [0, 0], [0, 0], [0.1, -0.2], [0.2, -0.1], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 68, "ty": 3, "parent": 67, "ks": {"p": {"a": 0, "k": [83.71, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 71, "ty": 4, "parent": 70, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.1, 0.5], [0, 0], [0, 0], [1.2, -0.7], [1.5, 0], [0, 0], [1, 0.5], [0.7, 0.9], [0.4, 1.4], [0, 1.8], [0, 0], [-0.4, 1.4], [-0.8, 1], [-1.1, 0.6], [-1.4, 0], [0, 0], [-0.9, -0.4], [-0.7, -0.8], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[20.3, 41.5], [18, 41.5], [17, 40.7], [17, 40.7], [16.7, 38.1], [13.6, 40.8], [9.6, 41.7], [9.6, 41.7], [6.2, 41], [3.7, 39], [2.1, 35.6], [1.5, 30.9], [1.5, 30.9], [2.1, 26.5], [4, 23], [6.8, 20.7], [10.7, 19.8], [10.7, 19.8], [14, 20.5], [16.5, 22.3], [16.5, 22.3], [16.5, 10.5], [20.3, 10.5], [20.3, 41.5]], "o": [[0, 0], [-0.5, 0], [0, 0], [0, 0], [-0.9, 1.1], [-1.2, 0.7], [0, 0], [-1.2, 0], [-1, -0.5], [-0.7, -0.9], [-0.4, -1.4], [0, 0], [0, -1.6], [0.4, -1.4], [0.8, -1], [1.1, -0.6], [0, 0], [1.3, 0], [0.9, 0.4], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.9, 0.6], [-0.8, 1], [0, 0], [0, 0], [0.8, 0.4], [1, 0], [0, 0], [1.1, -1.4], [0, -2.6], [0, 0], [-0.2, -1], [-0.5, -0.6], [-0.7, -0.3], [-0.9, 0]], "v": [[10.8, 38.7], [10.8, 38.7], [14, 37.9], [16.5, 35.5], [16.5, 35.5], [16.5, 25.2], [14.3, 23.3], [11.6, 22.7], [11.6, 22.7], [7, 24.8], [5.4, 30.9], [5.4, 30.9], [5.7, 34.5], [6.8, 36.9], [8.5, 38.3], [10.8, 38.7]], "o": [[0, 0], [1.2, 0], [0.9, -0.6], [0, 0], [0, 0], [-0.7, -0.9], [-0.8, -0.4], [0, 0], [-2, 0], [-1.1, 1.4], [0, 0], [0, 1.4], [0.2, 1], [0.5, 0.6], [0.7, 0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 70, "ty": 3, "parent": 67, "ks": {"p": {"a": 0, "k": [60.23, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 73, "ty": 4, "parent": 72, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.2, 0.1], [0.1, 0.4], [0, 0], [0, 0], [0.5, -0.4], [0.6, -0.3], [0.7, -0.1], [0.8, 0], [0, 0], [0.7, 0.2], [0.5, 0.5], [0.3, 0.7], [0, 1], [0, 0], [-0.5, 0.8], [-1, 0.6], [-1.7, 0.4], [-2.4, 0.1], [0, 0], [0, 0], [0.7, 0.8], [1.4, 0], [0, 0], [0.6, -0.2], [0.5, -0.3], [0.3, -0.2], [0.3, 0], [0, 0], [0.2, 0.1], [0.1, 0.2], [0, 0], [0, 0], [-1.4, 0.6], [-1.7, 0], [0, 0], [-0.9, -0.4], [-0.6, -0.7], [-0.3, -1], [0, -1.2], [0, 0]], "v": [[18.7, 27.8], [18.7, 41.5], [17, 41.5], [16.1, 41.3], [15.7, 40.6], [15.7, 40.6], [15.3, 38.6], [13.7, 40], [12, 41], [10.1, 41.6], [7.9, 41.8], [7.9, 41.8], [5.6, 41.4], [3.7, 40.4], [2.4, 38.7], [1.9, 36.1], [1.9, 36.1], [2.6, 33.7], [4.9, 31.6], [8.9, 30.2], [15, 29.5], [15, 29.5], [15, 27.8], [14, 24.1], [10.8, 22.8], [10.8, 22.8], [8.5, 23.2], [6.9, 24], [5.7, 24.7], [4.7, 25.1], [4.7, 25.1], [4.1, 24.9], [3.6, 24.4], [3.6, 24.4], [3, 23.2], [6.8, 20.6], [11.3, 19.8], [11.3, 19.8], [14.4, 20.4], [16.8, 22], [18.2, 24.6], [18.7, 27.8], [18.7, 27.8]], "o": [[0, 0], [0, 0], [-0.4, 0], [-0.2, -0.1], [0, 0], [0, 0], [-0.6, 0.5], [-0.5, 0.4], [-0.6, 0.3], [-0.7, 0.1], [0, 0], [-0.8, 0], [-0.7, -0.2], [-0.5, -0.5], [-0.3, -0.7], [0, 0], [0, -0.8], [0.5, -0.8], [1, -0.6], [1.7, -0.4], [0, 0], [0, 0], [0, -1.7], [-0.7, -0.8], [0, 0], [-0.9, 0], [-0.6, 0.2], [-0.5, 0.3], [-0.3, 0.2], [0, 0], [-0.3, 0], [-0.2, -0.1], [0, 0], [0, 0], [1.2, -1.1], [1.4, -0.6], [0, 0], [1.2, 0], [0.9, 0.4], [0.6, 0.7], [0.3, 1], [0, 0], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.5, 0.1], [-0.5, 0.2], [-0.4, 0.3], [-0.4, 0.4], [0, 0], [0, 0], [1.2, -0.2], [0.8, -0.3], [0.3, -0.5], [0, -0.6], [0, 0], [-0.2, -0.4], [-0.3, -0.3], [-0.4, -0.1], [-0.5, 0]], "v": [[9, 39.2], [9, 39.2], [10.8, 39], [12.3, 38.4], [13.7, 37.5], [15, 36.3], [15, 36.3], [15, 31.9], [10.6, 32.3], [7.7, 33.1], [6, 34.4], [5.5, 36], [5.5, 36], [5.8, 37.4], [6.5, 38.4], [7.6, 39], [9, 39.2]], "o": [[0, 0], [0.7, 0], [0.5, -0.1], [0.5, -0.2], [0.4, -0.3], [0, 0], [0, 0], [-1.7, 0.1], [-1.2, 0.2], [-0.8, 0.3], [-0.3, 0.5], [0, 0], [0, 0.6], [0.2, 0.4], [0.3, 0.3], [0.4, 0.1], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 72, "ty": 3, "parent": 67, "ks": {"p": {"a": 0, "k": [38.93, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 75, "ty": 4, "parent": 74, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-1.1, -0.4], [-0.8, -0.8], [-0.4, -1.2], [0, -1.5], [0, 0], [0.1, -0.2], [0.4, 0], [0, 0], [0, 0], [-0.3, -1], [-0.6, -0.7], [-0.8, -0.3], [-1, 0], [0, 0], [-0.7, 0.2], [-0.5, 0.3], [-0.3, 0.2], [-0.2, 0], [0, 0], [-0.2, -0.2], [0, 0], [0, 0], [0.6, -0.4], [0.7, -0.3], [0.8, -0.1], [0.8, 0], [0, 0], [1.2, 0.5], [0.9, 1], [0.5, 1.4], [0, 1.8], [0, 0], [-0.5, 1.3], [-0.9, 0.9], [-1.2, 0.5], [-1.5, 0]], "v": [[11.5, 19.8], [11.5, 19.8], [15, 20.5], [17.8, 22.3], [19.6, 25.3], [20.3, 29.4], [20.3, 29.4], [20.1, 30.5], [19.4, 30.8], [19.4, 30.8], [5.3, 30.8], [5.8, 34.3], [7.2, 36.8], [9.3, 38.3], [12, 38.8], [12, 38.8], [14.4, 38.5], [16.2, 37.8], [17.4, 37.1], [18.3, 36.8], [18.3, 36.8], [19, 37.1], [19, 37.1], [20, 38.5], [18.4, 39.9], [16.3, 41], [14, 41.5], [11.7, 41.7], [11.7, 41.7], [7.6, 41], [4.4, 38.8], [2.3, 35.3], [1.6, 30.4], [1.6, 30.4], [2.2, 26.2], [4.2, 22.9], [7.3, 20.7], [11.5, 19.8]], "o": [[0, 0], [1.3, 0], [1.1, 0.4], [0.8, 0.8], [0.4, 1.2], [0, 0], [0, 0.6], [-0.1, 0.2], [0, 0], [0, 0], [0, 1.3], [0.3, 1], [0.6, 0.7], [0.8, 0.3], [0, 0], [0.9, 0], [0.7, -0.2], [0.5, -0.3], [0.3, -0.2], [0, 0], [0.3, 0], [0, 0], [0, 0], [-0.5, 0.6], [-0.6, 0.4], [-0.7, 0.3], [-0.8, 0.1], [0, 0], [-1.5, 0], [-1.2, -0.5], [-0.9, -1], [-0.5, -1.4], [0, 0], [0, -1.5], [0.5, -1.3], [0.9, -0.9], [1.2, -0.5], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1, -1], [0.3, -1.8], [0, 0], [0, 0], [0.2, 0.7], [0.5, 0.5], [0.7, 0.3], [0.9, 0]], "v": [[11.6, 22.6], [11.6, 22.6], [7.3, 24.2], [5.4, 28.5], [5.4, 28.5], [16.9, 28.5], [16.6, 26.1], [15.5, 24.2], [13.8, 23], [11.6, 22.6]], "o": [[0, 0], [-1.8, 0], [-1, 1], [0, 0], [0, 0], [0, -0.9], [-0.2, -0.7], [-0.5, -0.5], [-0.7, -0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 74, "ty": 3, "parent": 67, "ks": {"p": {"a": 0, "k": [16.93, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 77, "ty": 4, "parent": 76, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.2, -0.2], [-0.1, -0.4], [0, 0], [0, 0], [-1.1, 0.8], [-1.4, 0], [0, 0], [-0.5, -0.1], [-0.4, -0.2], [0, 0], [0, 0], [0.3, 0], [0, 0], [0.4, 0.1], [0.7, 0], [0, 0], [0.9, -0.8], [0.6, -1.4], [0, 0], [0, 0]], "v": [[6.8, 41.5], [3.1, 41.5], [3.1, 20.2], [5.2, 20.2], [6, 20.4], [6.4, 21.2], [6.4, 21.2], [6.6, 24.5], [9.3, 21.1], [13.1, 19.8], [13.1, 19.8], [14.7, 20], [16, 20.6], [16, 20.6], [15.5, 23.4], [14.9, 23.9], [14.9, 23.9], [14, 23.7], [12.3, 23.5], [12.3, 23.5], [9, 24.6], [6.8, 27.9], [6.8, 27.9], [6.8, 41.5]], "o": [[0, 0], [0, 0], [0, 0], [0.4, 0], [0.2, 0.2], [0, 0], [0, 0], [0.7, -1.5], [1.1, -0.8], [0, 0], [0.6, 0], [0.5, 0.1], [0, 0], [0, 0], [-0.1, 0.3], [0, 0], [-0.2, 0], [-0.4, -0.1], [0, 0], [-1.3, 0], [-0.9, 0.8], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 76, "ty": 3, "parent": 67, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 67, "ty": 3, "ks": {"p": {"a": 0, "k": [0, -10]}}, "ip": 0, "op": 91, "st": 0}]}, {"id": "92", "layers": [{"ind": 83, "ty": 4, "parent": 82, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-1.1, -0.4], [-0.8, -0.8], [-0.4, -1.2], [0, -1.5], [0, 0], [0.1, -0.2], [0.4, 0], [0, 0], [0, 0], [-0.3, -1], [-0.6, -0.7], [-0.8, -0.3], [-1, 0], [0, 0], [-0.7, 0.2], [-0.5, 0.3], [-0.3, 0.2], [-0.2, 0], [0, 0], [-0.2, -0.2], [0, 0], [0, 0], [0.6, -0.4], [0.7, -0.3], [0.8, -0.1], [0.8, 0], [0, 0], [1.2, 0.5], [0.9, 1], [0.5, 1.4], [0, 1.8], [0, 0], [-0.5, 1.3], [-0.9, 0.9], [-1.2, 0.5], [-1.5, 0]], "v": [[11.5, 19.8], [11.5, 19.8], [15, 20.5], [17.8, 22.3], [19.6, 25.3], [20.3, 29.4], [20.3, 29.4], [20.1, 30.5], [19.4, 30.8], [19.4, 30.8], [5.3, 30.8], [5.8, 34.3], [7.2, 36.8], [9.3, 38.3], [12, 38.8], [12, 38.8], [14.4, 38.5], [16.2, 37.8], [17.4, 37.1], [18.3, 36.8], [18.3, 36.8], [19, 37.1], [19, 37.1], [20, 38.5], [18.4, 39.9], [16.3, 41], [14, 41.5], [11.7, 41.7], [11.7, 41.7], [7.6, 41], [4.4, 38.8], [2.3, 35.3], [1.6, 30.4], [1.6, 30.4], [2.2, 26.2], [4.2, 22.9], [7.3, 20.7], [11.5, 19.8]], "o": [[0, 0], [1.3, 0], [1.1, 0.4], [0.8, 0.8], [0.4, 1.2], [0, 0], [0, 0.6], [-0.1, 0.2], [0, 0], [0, 0], [0, 1.3], [0.3, 1], [0.6, 0.7], [0.8, 0.3], [0, 0], [0.9, 0], [0.7, -0.2], [0.5, -0.3], [0.3, -0.2], [0, 0], [0.3, 0], [0, 0], [0, 0], [-0.5, 0.6], [-0.6, 0.4], [-0.7, 0.3], [-0.8, 0.1], [0, 0], [-1.5, 0], [-1.2, -0.5], [-0.9, -1], [-0.5, -1.4], [0, 0], [0, -1.5], [0.5, -1.3], [0.9, -0.9], [1.2, -0.5], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1, -1], [0.3, -1.8], [0, 0], [0, 0], [0.2, 0.7], [0.5, 0.5], [0.7, 0.3], [0.9, 0]], "v": [[11.6, 22.6], [11.6, 22.6], [7.3, 24.2], [5.4, 28.5], [5.4, 28.5], [16.9, 28.5], [16.6, 26.1], [15.5, 24.2], [13.8, 23], [11.6, 22.6]], "o": [[0, 0], [-1.8, 0], [-1, 1], [0, 0], [0, 0], [0, -0.9], [-0.2, -0.7], [-0.5, -0.5], [-0.7, -0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 82, "ty": 3, "parent": 81, "ks": {"p": {"a": 0, "k": [88.79, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 85, "ty": 4, "parent": 84, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.2, -0.2], [-0.1, -0.4], [0, 0], [0, 0], [-1.1, 0.8], [-1.4, 0], [0, 0], [-0.5, -0.1], [-0.4, -0.2], [0, 0], [0, 0], [0.3, 0], [0, 0], [0.4, 0.1], [0.7, 0], [0, 0], [0.9, -0.8], [0.6, -1.4], [0, 0], [0, 0]], "v": [[6.8, 41.5], [3.1, 41.5], [3.1, 20.2], [5.2, 20.2], [6, 20.4], [6.4, 21.2], [6.4, 21.2], [6.6, 24.5], [9.3, 21.1], [13.1, 19.8], [13.1, 19.8], [14.7, 20], [16, 20.6], [16, 20.6], [15.5, 23.4], [14.9, 23.9], [14.9, 23.9], [14, 23.7], [12.3, 23.5], [12.3, 23.5], [9, 24.6], [6.8, 27.9], [6.8, 27.9], [6.8, 41.5]], "o": [[0, 0], [0, 0], [0, 0], [0.4, 0], [0.2, 0.2], [0, 0], [0, 0], [0.7, -1.5], [1.1, -0.8], [0, 0], [0.6, 0], [0.5, 0.1], [0, 0], [0, 0], [-0.1, 0.3], [0, 0], [-0.2, 0], [-0.4, -0.1], [0, 0], [-1.3, 0], [-0.9, 0.8], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 84, "ty": 3, "parent": 81, "ks": {"p": {"a": 0, "k": [71.86, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 87, "ty": 4, "parent": 86, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.2, -0.2], [0.4, 0], [0, 0], [0.2, 0.2], [0.1, 0.4], [0, 0], [0, 0], [0, 0]], "v": [[3.2, 11.4], [6.4, 11.4], [6.4, 17.5], [6.1, 20.7], [5.8, 21.7], [4.8, 22.1], [4.8, 22.1], [4, 21.7], [3.5, 20.7], [3.5, 20.7], [3.2, 17.5], [3.2, 11.4]], "o": [[0, 0], [0, 0], [0, 0], [0, 0.4], [-0.2, 0.2], [0, 0], [-0.4, 0], [-0.2, -0.2], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 86, "ty": 3, "parent": 81, "ks": {"p": {"a": 0, "k": [62.2, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 89, "ty": 4, "parent": 88, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-1.1, -0.4], [-0.8, -0.8], [-0.4, -1.2], [0, -1.5], [0, 0], [0.1, -0.2], [0.4, 0], [0, 0], [0, 0], [-0.3, -1], [-0.6, -0.7], [-0.8, -0.3], [-1, 0], [0, 0], [-0.7, 0.2], [-0.5, 0.3], [-0.3, 0.2], [-0.2, 0], [0, 0], [-0.2, -0.2], [0, 0], [0, 0], [0.6, -0.4], [0.7, -0.3], [0.8, -0.1], [0.8, 0], [0, 0], [1.2, 0.5], [0.9, 1], [0.5, 1.4], [0, 1.8], [0, 0], [-0.5, 1.3], [-0.9, 0.9], [-1.2, 0.5], [-1.5, 0]], "v": [[11.5, 19.8], [11.5, 19.8], [15, 20.5], [17.8, 22.3], [19.6, 25.3], [20.3, 29.4], [20.3, 29.4], [20.1, 30.5], [19.4, 30.8], [19.4, 30.8], [5.3, 30.8], [5.8, 34.3], [7.2, 36.8], [9.3, 38.3], [12, 38.8], [12, 38.8], [14.4, 38.5], [16.2, 37.8], [17.4, 37.1], [18.3, 36.8], [18.3, 36.8], [19, 37.1], [19, 37.1], [20, 38.5], [18.4, 39.9], [16.3, 41], [14, 41.5], [11.7, 41.7], [11.7, 41.7], [7.6, 41], [4.4, 38.8], [2.3, 35.3], [1.6, 30.4], [1.6, 30.4], [2.2, 26.2], [4.2, 22.9], [7.3, 20.7], [11.5, 19.8]], "o": [[0, 0], [1.3, 0], [1.1, 0.4], [0.8, 0.8], [0.4, 1.2], [0, 0], [0, 0.6], [-0.1, 0.2], [0, 0], [0, 0], [0, 1.3], [0.3, 1], [0.6, 0.7], [0.8, 0.3], [0, 0], [0.9, 0], [0.7, -0.2], [0.5, -0.3], [0.3, -0.2], [0, 0], [0.3, 0], [0, 0], [0, 0], [-0.5, 0.6], [-0.6, 0.4], [-0.7, 0.3], [-0.8, 0.1], [0, 0], [-1.5, 0], [-1.2, -0.5], [-0.9, -1], [-0.5, -1.4], [0, 0], [0, -1.5], [0.5, -1.3], [0.9, -0.9], [1.2, -0.5], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1, -1], [0.3, -1.8], [0, 0], [0, 0], [0.2, 0.7], [0.5, 0.5], [0.7, 0.3], [0.9, 0]], "v": [[11.6, 22.6], [11.6, 22.6], [7.3, 24.2], [5.4, 28.5], [5.4, 28.5], [16.9, 28.5], [16.6, 26.1], [15.5, 24.2], [13.8, 23], [11.6, 22.6]], "o": [[0, 0], [-1.8, 0], [-1, 1], [0, 0], [0, 0], [0, -0.9], [-0.2, -0.7], [-0.5, -0.5], [-0.7, -0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 88, "ty": 3, "parent": 81, "ks": {"p": {"a": 0, "k": [42.13, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 91, "ty": 4, "parent": 90, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [-0.2, -0.2], [-0.1, -0.3], [0, 0], [0, 0], [-0.1, -0.4], [-0.1, -0.5], [0, 0], [-0.1, 0.4], [-0.1, 0.4], [0, 0], [0, 0], [-0.2, 0.2], [-0.3, 0], [0, 0], [0, 0], [-0.2, -0.2], [-0.1, -0.3], [0, 0], [0, 0], [-0.2, -0.9], [0, 0], [-0.1, 0.4], [-0.1, 0.4], [0, 0], [0, 0], [-0.2, 0.2], [-0.4, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.1, 0.6], [0, 0], [0.1, -0.3], [0.1, -0.2], [0, 0], [0, 0], [0, 0]], "v": [[9.7, 41.5], [0.3, 11.4], [3.7, 11.4], [4.6, 11.6], [5.1, 12.3], [5.1, 12.3], [11.3, 33.2], [11.6, 34.5], [11.8, 35.8], [11.8, 35.8], [12.1, 34.5], [12.5, 33.2], [12.5, 33.2], [19.6, 12.3], [20, 11.7], [20.9, 11.4], [20.9, 11.4], [22.1, 11.4], [23, 11.6], [23.5, 12.3], [23.5, 12.3], [30.5, 33.2], [31.2, 35.7], [31.2, 35.7], [31.4, 34.4], [31.7, 33.2], [31.7, 33.2], [37.9, 12.3], [38.4, 11.7], [39.3, 11.4], [39.3, 11.4], [42.5, 11.4], [33.1, 41.5], [29.4, 41.5], [21.8, 18.5], [21.4, 17], [21.4, 17], [21.2, 17.8], [21, 18.5], [21, 18.5], [13.3, 41.5], [9.7, 41.5]], "o": [[0, 0], [0, 0], [0.4, 0], [0.2, 0.2], [0, 0], [0, 0], [0.1, 0.4], [0.1, 0.4], [0, 0], [0.1, -0.5], [0.1, -0.4], [0, 0], [0, 0], [0.1, -0.2], [0.2, -0.2], [0, 0], [0, 0], [0.4, 0], [0.2, 0.2], [0, 0], [0, 0], [0.3, 0.7], [0, 0], [0.1, -0.5], [0.1, -0.4], [0, 0], [0, 0], [0.1, -0.3], [0.2, -0.2], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.2, -0.4], [0, 0], [-0.1, 0.3], [-0.1, 0.3], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 90, "ty": 3, "parent": 81, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 81, "ty": 3, "ks": {"p": {"a": 0, "k": [0, -11]}}, "ip": 0, "op": 91, "st": 0}]}, {"id": "102", "layers": [{"ind": 99, "ty": 4, "parent": 98, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.3, 0.4], [0.9, 0.8], [0.5, 1.1], [0, 1.3], [0, 0], [-0.9, 1.2], [-1.9, 0.6], [0, 0], [0.7, 1.1], [0, 1.6], [0, 0], [-0.5, 1], [-0.8, 0.7], [-1.2, 0.4], [-1.4, 0], [0, 0], [-1.2, -0.4], [-0.8, -0.7], [-0.5, -1], [0, -1.1], [0, 0], [0.7, -1.1], [1.5, -0.6], [0, 0], [-0.9, -1.2], [0, -1.8], [0, 0], [0.5, -1.1], [0.9, -0.8], [1.3, -0.4], [1.6, 0]], "v": [[12.2, 41.8], [12.2, 41.8], [7.9, 41.2], [4.6, 39.4], [2.4, 36.6], [1.6, 32.9], [1.6, 32.9], [3, 28.4], [7.1, 25.6], [7.1, 25.6], [3.7, 23], [2.6, 18.9], [2.6, 18.9], [3.3, 15.7], [5.3, 13.1], [8.3, 11.4], [12.2, 10.8], [12.2, 10.8], [16, 11.4], [19, 13.1], [21, 15.7], [21.7, 18.9], [21.7, 18.9], [20.6, 23], [17.3, 25.6], [17.3, 25.6], [21.4, 28.4], [22.7, 32.9], [22.7, 32.9], [22, 36.6], [19.8, 39.4], [16.5, 41.2], [12.2, 41.8]], "o": [[0, 0], [-1.6, 0], [-1.3, -0.4], [-0.9, -0.8], [-0.5, -1.1], [0, 0], [0, -1.8], [0.9, -1.2], [0, 0], [-1.5, -0.6], [-0.7, -1.1], [0, 0], [0, -1.1], [0.5, -1], [0.8, -0.7], [1.2, -0.4], [0, 0], [1.4, 0], [1.2, 0.4], [0.8, 0.7], [0.5, 1], [0, 0], [0, 1.6], [-0.7, 1.1], [0, 0], [1.9, 0.6], [0.9, 1.2], [0, 0], [0, 1.3], [-0.5, 1.1], [-0.9, 0.8], [-1.3, 0.4], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.6, 0.2], [-0.4, 0.4], [-0.2, 0.6], [0, 0.7], [0, 0], [0.9, 0.9], [1.6, 0], [0, 0], [0.9, -0.9], [0, -1.7], [0, 0], [-0.2, -0.6], [-0.4, -0.4], [-0.6, -0.2], [-0.9, 0]], "v": [[12.2, 37.7], [12.2, 37.7], [14.4, 37.4], [16.1, 36.4], [17.1, 34.8], [17.4, 32.8], [17.4, 32.8], [16, 29.1], [12.2, 27.8], [12.2, 27.8], [8.4, 29.1], [7, 32.8], [7, 32.8], [7.3, 34.8], [8.3, 36.4], [9.9, 37.4], [12.2, 37.7]], "o": [[0, 0], [0.9, 0], [0.6, -0.2], [0.4, -0.4], [0.2, -0.6], [0, 0], [0, -1.7], [-0.9, -0.9], [0, 0], [-1.6, 0], [-0.9, 0.9], [0, 0], [0, 0.7], [0.2, 0.6], [0.4, 0.4], [0.6, 0.2], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.6, 0.3], [-0.4, 0.4], [-0.1, 0.6], [0, 0.6], [0, 0], [0.2, 0.5], [0.4, 0.4], [0.6, 0.2], [0.8, 0], [0, 0], [0.6, -0.2], [0.4, -0.4], [0.2, -0.5], [0, -0.6], [0, 0], [-0.1, -0.6], [-0.4, -0.4], [-0.6, -0.3], [-0.8, 0]], "v": [[12.2, 23.7], [12.2, 23.7], [14.3, 23.3], [15.7, 22.2], [16.4, 20.7], [16.7, 19], [16.7, 19], [16.4, 17.3], [15.6, 16], [14.2, 15], [12.2, 14.7], [12.2, 14.7], [10.2, 15], [8.8, 16], [8, 17.3], [7.7, 19], [7.7, 19], [7.9, 20.7], [8.7, 22.2], [10.1, 23.3], [12.2, 23.7]], "o": [[0, 0], [0.8, 0], [0.6, -0.3], [0.3, -0.4], [0.1, -0.6], [0, 0], [0, -0.6], [-0.2, -0.5], [-0.4, -0.4], [-0.6, -0.2], [0, 0], [-0.8, 0], [-0.6, 0.2], [-0.4, 0.4], [-0.2, 0.5], [0, 0], [0, 0.6], [0.1, 0.6], [0.4, 0.4], [0.6, 0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 98, "ty": 3, "parent": 97, "ks": {"p": {"a": 0, "k": [24.36, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 101, "ty": 4, "parent": 100, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0.8], [0, 0], [0, 0], [0.2, -0.1], [0.2, 0], [0, 0], [0.2, 0.1], [0.1, 0.2], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.6, 41.5], [5.6, 37.6], [11.8, 37.6], [11.8, 19.9], [11.9, 17.7], [11.9, 17.7], [7.5, 21.4], [6.9, 21.7], [6.4, 21.8], [6.4, 21.8], [5.6, 21.6], [5.1, 21.2], [5.1, 21.2], [3.5, 18.9], [12.7, 11], [17, 11], [17, 37.6], [22.4, 37.6], [22.4, 41.5], [5.6, 41.5]], "o": [[0, 0], [0, 0], [0, 0], [0, -0.7], [0, 0], [0, 0], [-0.2, 0.2], [-0.2, 0.1], [0, 0], [-0.3, 0], [-0.2, -0.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 100, "ty": 3, "parent": 97, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 97, "ty": 3, "ks": {"p": {"a": 0, "k": [-1, -10]}}, "ip": 0, "op": 91, "st": 0}]}, {"id": "114", "layers": [{"ind": 107, "ty": 4, "parent": 106, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1, 1.1], [0, 1.9], [0, 0], [0, 0], [0, 0], [0.2, 0.2], [0, 0.4], [0, 0], [0, 0], [0, 0], [0, 0], [-0.2, 0.2], [-0.3, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.3, -0.4], [-0.6, 0], [0, 0], [-0.2, 0.1], [-0.2, 0.1], [-0.1, 0.1], [-0.1, 0], [0, 0], [-0.1, -0.1], [-0.1, -0.2], [0, 0], [0, 0], [1, -0.3], [1.1, 0]], "v": [[9.8, 41.8], [9.8, 41.8], [5.5, 40.2], [4, 35.8], [4, 35.8], [4, 23.8], [1.8, 23.8], [1.1, 23.5], [0.8, 22.7], [0.8, 22.7], [0.8, 20.6], [4.3, 20.1], [5.4, 14.2], [5.8, 13.5], [6.5, 13.3], [6.5, 13.3], [9.2, 13.3], [9.2, 20.1], [14.9, 20.1], [14.9, 23.8], [9.2, 23.8], [9.2, 35.5], [9.7, 37], [11.1, 37.6], [11.1, 37.6], [11.9, 37.5], [12.4, 37.3], [12.9, 37], [13.3, 36.9], [13.3, 36.9], [13.6, 37], [13.9, 37.4], [13.9, 37.4], [15.5, 39.9], [12.9, 41.3], [9.8, 41.8]], "o": [[0, 0], [-1.9, 0], [-1, -1.1], [0, 0], [0, 0], [0, 0], [-0.3, 0], [-0.2, -0.2], [0, 0], [0, 0], [0, 0], [0, 0], [0.1, -0.3], [0.2, -0.2], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.7], [0.3, 0.4], [0, 0], [0.3, 0], [0.2, -0.1], [0.2, -0.1], [0.1, -0.1], [0, 0], [0.2, 0], [0.1, 0.1], [0, 0], [0, 0], [-0.8, 0.6], [-1, 0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 106, "ty": 3, "parent": 105, "ks": {"p": {"a": 0, "k": [56.95, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 109, "ty": 4, "parent": 108, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.3, 0.1], [0.1, 0.4], [0, 0], [0, 0], [0.5, -0.4], [0.6, -0.3], [0.6, -0.1], [0.8, 0], [0, 0], [0.8, 0.2], [0.6, 0.5], [0.3, 0.7], [0, 1], [0, 0], [-0.4, 0.8], [-1, 0.6], [-1.7, 0.4], [-2.4, 0.1], [0, 0], [0, 0], [0.6, 0.7], [1.2, 0], [0, 0], [0.6, -0.2], [0.4, -0.2], [0.3, -0.2], [0.4, 0], [0, 0], [0.3, 0.2], [0.2, 0.3], [0, 0], [0, 0], [-3.5, 0], [0, 0], [-1, -0.4], [-0.7, -0.7], [-0.4, -1], [0, -1.2], [0, 0]], "v": [[19.7, 27.8], [19.7, 41.5], [17.3, 41.5], [16.2, 41.2], [15.5, 40.3], [15.5, 40.3], [15.1, 38.8], [13.5, 40.1], [11.8, 41], [10.1, 41.6], [8, 41.8], [8, 41.8], [5.4, 41.4], [3.5, 40.3], [2.2, 38.5], [1.7, 36], [1.7, 36], [2.3, 33.5], [4.5, 31.4], [8.4, 29.8], [14.6, 29.1], [14.6, 29.1], [14.6, 27.8], [13.7, 24.6], [11, 23.6], [11, 23.6], [8.9, 23.9], [7.5, 24.6], [6.3, 25.2], [5.2, 25.5], [5.2, 25.5], [4.3, 25.2], [3.7, 24.6], [3.7, 24.6], [2.7, 22.9], [11.7, 19.5], [11.7, 19.5], [15, 20.1], [17.6, 21.9], [19.1, 24.5], [19.7, 27.8], [19.7, 27.8]], "o": [[0, 0], [0, 0], [-0.5, 0], [-0.3, -0.1], [0, 0], [0, 0], [-0.5, 0.5], [-0.5, 0.4], [-0.6, 0.3], [-0.6, 0.1], [0, 0], [-0.9, 0], [-0.8, -0.2], [-0.6, -0.5], [-0.3, -0.7], [0, 0], [0, -0.8], [0.4, -0.8], [1, -0.6], [1.7, -0.4], [0, 0], [0, 0], [0, -1.4], [-0.6, -0.7], [0, 0], [-0.8, 0], [-0.6, 0.2], [-0.4, 0.2], [-0.4, 0.2], [0, 0], [-0.4, 0], [-0.3, -0.2], [0, 0], [0, 0], [2.5, -2.3], [0, 0], [1.3, 0], [1, 0.4], [0.7, 0.7], [0.4, 1], [0, 0], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.4, 0.1], [-0.4, 0.2], [-0.4, 0.3], [-0.4, 0.4], [0, 0], [0, 0], [1, -0.2], [0.6, -0.3], [0.3, -0.4], [0, -0.5], [0, 0], [-0.5, -0.4], [-0.9, 0]], "v": [[9.6, 38.2], [9.6, 38.2], [11, 38.1], [12.3, 37.6], [13.5, 36.9], [14.6, 35.9], [14.6, 35.9], [14.6, 32.3], [10.8, 32.6], [8.4, 33.4], [7, 34.4], [6.7, 35.7], [6.7, 35.7], [7.5, 37.6], [9.6, 38.2]], "o": [[0, 0], [0.5, 0], [0.4, -0.1], [0.4, -0.2], [0.4, -0.3], [0, 0], [0, 0], [-1.5, 0.1], [-1, 0.2], [-0.6, 0.3], [-0.3, 0.4], [0, 0], [0, 0.9], [0.5, 0.4], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 108, "ty": 3, "parent": 105, "ks": {"p": {"a": 0, "k": [34.97, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 111, "ty": 4, "parent": 110, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.1, 10.2], [8.3, 10.2], [8.3, 41.5], [3.1, 41.5], [3.1, 10.2]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 110, "ty": 3, "parent": 105, "ks": {"p": {"a": 0, "k": [23.58, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 113, "ty": 4, "parent": 112, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.1, 11.1], [22.2, 11.1], [22.2, 15.6], [8.8, 15.6], [8.8, 24.6], [20.1, 24.6], [20.1, 29.1], [8.8, 29.1], [8.8, 41.5], [3.1, 41.5], [3.1, 11.1]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 112, "ty": 3, "parent": 105, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 105, "ty": 3, "ks": {"p": {"a": 0, "k": [0, -10]}}, "ip": 0, "op": 91, "st": 0}]}, {"id": "126", "layers": [{"ind": 121, "ty": 4, "parent": 120, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [-0.2, -0.1], [-0.1, -0.2], [0, 0], [0, 0], [-0.1, -0.4], [-0.1, -0.4], [0, 0], [-0.1, 0.4], [-0.1, 0.4], [0, 0], [0, 0], [-0.1, 0.1], [-0.2, 0], [0, 0], [0, 0], [-0.2, -0.1], [-0.1, -0.2], [0, 0], [0, 0], [-0.1, -0.4], [-0.1, -0.4], [0, 0], [-0.1, 0.4], [-0.1, 0.4], [0, 0], [0, 0], [-0.2, 0.1], [-0.2, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.1, 0.4], [0, 0], [0, 0], [0.1, 0.3], [0.1, 0.3], [0, 0], [0.1, -0.3], [0.1, -0.3], [0, 0], [0, 0], [0.4, 0], [0, 0], [0, 0]], "v": [[6.2, 35.5], [0.3, 17.3], [2.8, 17.3], [3.4, 17.5], [3.8, 18], [3.8, 18], [7.3, 29.7], [7.5, 31], [7.7, 32.2], [7.7, 32.2], [8.1, 31], [8.4, 29.7], [8.4, 29.7], [12.3, 17.9], [12.6, 17.4], [13.2, 17.3], [13.2, 17.3], [14.6, 17.3], [15.2, 17.4], [15.5, 17.9], [15.5, 17.9], [19.2, 29.7], [19.6, 30.9], [19.9, 32.1], [19.9, 32.1], [20.1, 30.9], [20.4, 29.7], [20.4, 29.7], [24, 18], [24.3, 17.5], [24.9, 17.3], [24.9, 17.3], [27.3, 17.3], [21.4, 35.5], [18.9, 35.5], [18.3, 34.9], [18.3, 34.9], [14.2, 22.6], [14, 21.7], [13.8, 20.9], [13.8, 20.9], [13.6, 21.7], [13.4, 22.6], [13.4, 22.6], [9.3, 34.9], [8.6, 35.5], [8.6, 35.5], [6.2, 35.5]], "o": [[0, 0], [0, 0], [0.3, 0], [0.2, 0.1], [0, 0], [0, 0], [0.1, 0.4], [0.1, 0.4], [0, 0], [0.1, -0.4], [0.1, -0.4], [0, 0], [0, 0], [0.1, -0.2], [0.2, -0.1], [0, 0], [0, 0], [0.2, 0], [0.2, 0.1], [0, 0], [0, 0], [0.1, 0.4], [0.1, 0.4], [0, 0], [0.1, -0.4], [0.1, -0.4], [0, 0], [0, 0], [0.1, -0.2], [0.2, -0.1], [0, 0], [0, 0], [0, 0], [0, 0], [-0.3, 0], [0, 0], [0, 0], [-0.1, -0.3], [-0.1, -0.3], [0, 0], [-0.1, 0.3], [-0.1, 0.3], [0, 0], [0, 0], [-0.1, 0.4], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 120, "ty": 3, "parent": 119, "ks": {"p": {"a": 0, "k": [40.03, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 123, "ty": 4, "parent": 122, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-1.1, -0.4], [-0.7, -0.8], [-0.4, -1.2], [0, -1.4], [0, 0], [0.4, -1.2], [0.8, -0.8], [1.1, -0.4], [1.3, 0], [0, 0], [1.1, 0.4], [0.8, 0.8], [0.4, 1.2], [0, 1.4], [0, 0], [-0.4, 1.2], [-0.8, 0.8], [-1.1, 0.4], [-1.3, 0]], "v": [[10, 17], [10, 17], [13.6, 17.7], [16.4, 19.6], [18.1, 22.5], [18.7, 26.4], [18.7, 26.4], [18.1, 30.3], [16.4, 33.2], [13.6, 35.1], [10, 35.8], [10, 35.8], [6.4, 35.1], [3.7, 33.2], [1.9, 30.3], [1.3, 26.4], [1.3, 26.4], [1.9, 22.5], [3.7, 19.6], [6.4, 17.7], [10, 17]], "o": [[0, 0], [1.3, 0], [1.1, 0.4], [0.8, 0.8], [0.4, 1.2], [0, 0], [0, 1.4], [-0.4, 1.2], [-0.7, 0.8], [-1.1, 0.4], [0, 0], [-1.3, 0], [-1.1, -0.4], [-0.8, -0.8], [-0.4, -1.2], [0, 0], [0, -1.4], [0.4, -1.2], [0.8, -0.8], [1.1, -0.4], [0, 0]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.9, 1.2], [0, 2.2], [0, 0], [0.9, 1.2], [1.8, 0], [0, 0], [0.7, -0.3], [0.5, -0.6], [0.2, -0.9], [0, -1.1], [0, 0], [-0.2, -0.9], [-0.4, -0.6], [-0.7, -0.3], [-0.9, 0]], "v": [[10, 33.3], [10, 33.3], [14, 31.5], [15.4, 26.4], [15.4, 26.4], [14, 21.3], [10, 19.5], [10, 19.5], [7.6, 20], [5.9, 21.3], [4.9, 23.5], [4.6, 26.4], [4.6, 26.4], [4.9, 29.3], [5.9, 31.5], [7.6, 32.8], [10, 33.3]], "o": [[0, 0], [1.8, 0], [0.9, -1.2], [0, 0], [0, -2.2], [-0.9, -1.2], [0, 0], [-0.9, 0], [-0.7, 0.3], [-0.4, 0.6], [-0.2, 0.9], [0, 0], [0, 1.1], [0.2, 0.9], [0.5, 0.6], [0.7, 0.3], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 122, "ty": 3, "parent": 119, "ks": {"p": {"a": 0, "k": [20.02, 0]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 125, "ty": 4, "parent": 124, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.1, -0.4], [0, 0], [0, 0], [-1, 0.5], [-1.3, 0], [0, 0], [-0.8, -0.3], [-0.5, -0.6], [-0.3, -0.9], [0, -1], [0, 0], [0, 0], [0, 0], [0, 0], [0.6, 0.8], [1.3, 0], [0, 0], [0.8, -0.5], [0.7, -0.8], [0, 0], [0, 0]], "v": [[5.8, 35.5], [2.6, 35.5], [2.6, 17.3], [4.5, 17.3], [5.4, 18], [5.4, 18], [5.7, 19.9], [8.3, 17.8], [11.7, 17], [11.7, 17], [14.3, 17.5], [16.2, 18.9], [17.4, 21.1], [17.8, 23.9], [17.8, 23.9], [17.8, 35.5], [14.6, 35.5], [14.6, 23.9], [13.7, 20.7], [10.8, 19.6], [10.8, 19.6], [8.1, 20.2], [5.8, 22.1], [5.8, 22.1], [5.8, 35.5]], "o": [[0, 0], [0, 0], [0, 0], [0.5, 0], [0, 0], [0, 0], [0.8, -0.9], [1, -0.5], [0, 0], [1, 0], [0.8, 0.3], [0.5, 0.6], [0.3, 0.9], [0, 0], [0, 0], [0, 0], [0, 0], [0, -1.4], [-0.6, -0.8], [0, 0], [-0.9, 0], [-0.8, 0.5], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 124, "ty": 3, "parent": 119, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 119, "ty": 3, "parent": 118, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 118, "ty": 3, "parent": 117, "ks": {"p": {"a": 0, "k": [0.39, 6.4]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 117, "ty": 3, "ks": {"p": {"a": 0, "k": [0, -17]}}, "ip": 0, "op": 91, "st": 0}]}, {"id": "132", "layers": [{"ind": 131, "ty": 4, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [33.07, 33.07]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [66.15, 66.15]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12.91, -10.75], [0, -15.2], [-20.71, -10.34], [5.26, -5.08], [-8.2, 5.43], [-6.59, -0.01], [-12.91, 10.75], [0, 15.2], [12.91, 10.75], [18.25, 0]], "o": [[-18.25, 0], [-12.91, 10.75], [0.04, 20.12], [-2.71, 6.07], [10.19, -1.79], [6.32, 1.56], [18.25, 0], [12.91, -10.75], [0, -15.2], [-12.91, -10.75], [0, 0]], "v": [[93.75, 32.46], [45.09, 49.25], [24.93, 89.78], [58.36, 138.86], [46.33, 155.71], [74.28, 144.73], [93.75, 147.1], [142.41, 130.31], [162.57, 89.78], [142.41, 49.25], [93.75, 32.46]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, -18.49], [0, 0], [22.8, 0], [0, 0], [0, 18.49], [0, 0], [-22.8, 0]], "o": [[0, 0], [22.8, 0], [0, 0], [0, 22.8], [0, 0], [-22.8, 0], [0, 0], [0, -22.8], [0, 0]], "v": [[41.29, 0], [146.2, 0], [187.5, 41.29], [187.5, 146.2], [146.2, 187.5], [41.29, 187.5], [0, 146.2], [0, 41.29], [41.29, 0]]}}}, {"ty": "gf", "e": {"a": 0, "k": [95.24, 23.27]}, "g": {"p": 2, "k": {"a": 0, "k": [0, 0.05, 0.74, 0.16, 1, 0.36, 0.96, 0.46, 0, 1, 1, 1]}}, "t": 1, "o": {"a": 0, "k": 100}, "s": {"a": 0, "k": [95.24, 175.05]}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "135", "layers": [{"ind": 134, "ty": 0, "parent": 130, "ks": {"s": {"a": 0, "k": [26.46, 26.46]}}, "w": 250, "h": 250, "ip": 0, "op": 91, "st": 0, "refId": "132"}, {"ind": 130, "ty": 3, "ks": {"s": {"a": 0, "k": [181.42, 181.42]}}, "ip": 0, "op": 91, "st": 0}]}, {"id": "139", "layers": [{"ind": 138, "ty": 4, "td": 1, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [60, 60]}, "r": {"a": 0, "k": 12}, "s": {"a": 0, "k": [120, 120]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 137, "ty": 0, "tt": 1, "ks": {}, "w": 120, "h": 120, "ip": 0, "op": 91, "st": 0, "refId": "135"}]}, {"id": "143", "layers": [{"ind": 142, "ty": 4, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [480, 96]}, "r": {"a": 0, "k": 56}, "s": {"a": 0, "k": [960, 192]}}, {"ty": "fl", "c": {"a": 0, "k": [0.9, 0.9, 0.9]}, "o": {"a": 0, "k": 100}}]}]}, {"id": "146", "layers": [{"ind": 12, "ty": 0, "parent": 5, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 72, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 90, "s": [100], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [621.85, 25.2], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 72, "s": [621.85, 25.2], "i": {"x": [1, 0.25], "y": [1, 1]}, "o": {"x": [0, 0.25], "y": [0, 0.1]}}, {"t": 90, "s": [621.85, 0], "h": 1}]}}, "w": 84, "h": 47, "ip": 0, "op": 91, "st": 0, "refId": "10"}, {"ind": 32, "ty": 0, "parent": 5, "ks": {"a": {"a": 0, "k": [2, -10]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 67.5, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 85.5, "s": [100], "h": 1}, {"t": 90, "s": [100], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [467.92, 25.2], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 67.5, "s": [467.92, 25.2], "i": {"x": [1, 0.25], "y": [1, 1]}, "o": {"x": [0, 0.25], "y": [0, 0.1]}}, {"t": 85.5, "s": [467.92, 0], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 90, "s": [467.92, 0], "h": 1}]}}, "w": 144, "h": 40, "ip": 0, "op": 91, "st": 0, "refId": "30"}, {"ind": 44, "ty": 0, "parent": 5, "ks": {"a": {"a": 0, "k": [0, -19]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 63, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 81, "s": [100], "h": 1}, {"t": 90, "s": [100], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [375.23, 25.2], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 63, "s": [375.23, 25.2], "i": {"x": [1, 0.25], "y": [1, 1]}, "o": {"x": [0, 0.25], "y": [0, 0.1]}}, {"t": 81, "s": [375.23, 0], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 90, "s": [375.23, 0], "h": 1}]}}, "w": 84, "h": 30, "ip": 0, "op": 91, "st": 0, "refId": "42"}, {"ind": 58, "ty": 0, "parent": 5, "ks": {"a": {"a": 0, "k": [0, -13]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 58.5, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 76.5, "s": [100], "h": 1}, {"t": 90, "s": [100], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [279.34, 25.2], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 58.5, "s": [279.34, 25.2], "i": {"x": [1, 0.25], "y": [1, 1]}, "o": {"x": [0, 0.25], "y": [0, 0.1]}}, {"t": 76.5, "s": [279.34, 0], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 90, "s": [279.34, 0], "h": 1}]}}, "w": 87, "h": 29, "ip": 0, "op": 91, "st": 0, "refId": "56"}, {"ind": 66, "ty": 0, "parent": 5, "ks": {"a": {"a": 0, "k": [0, -13]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 54, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 72, "s": [100], "h": 1}, {"t": 90, "s": [100], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [232.22, 25.2], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 54, "s": [232.22, 25.2], "i": {"x": [1, 0.25], "y": [1, 1]}, "o": {"x": [0, 0.25], "y": [0, 0.1]}}, {"t": 72, "s": [232.22, 0], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 90, "s": [232.22, 0], "h": 1}]}}, "w": 38, "h": 29, "ip": 0, "op": 91, "st": 0, "refId": "64"}, {"ind": 80, "ty": 0, "parent": 5, "ks": {"a": {"a": 0, "k": [0, -10]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 49.5, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 67.5, "s": [100], "h": 1}, {"t": 90, "s": [100], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [118.9, 25.2], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 49.5, "s": [118.9, 25.2], "i": {"x": [1, 0.25], "y": [1, 1]}, "o": {"x": [0, 0.25], "y": [0, 0.1]}}, {"t": 67.5, "s": [118.9, 0], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 90, "s": [118.9, 0], "h": 1}]}}, "w": 105, "h": 39, "ip": 0, "op": 91, "st": 0, "refId": "78"}, {"ind": 94, "ty": 0, "parent": 5, "ks": {"a": {"a": 0, "k": [0, -11]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 45, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 63, "s": [100], "h": 1}, {"t": 90, "s": [100], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [0, 25.2], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 45, "s": [0, 25.2], "i": {"x": [1, 0.25], "y": [1, 1]}, "o": {"x": [0, 0.25], "y": [0, 0.1]}}, {"t": 63, "s": [0, 0], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 90, "s": [0, 0], "h": 1}]}}, "w": 110, "h": 31, "ip": 0, "op": 91, "st": 0, "refId": "92"}, {"ind": 5, "ty": 3, "parent": 4, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 4, "ty": 3, "parent": 3, "ks": {"p": {"a": 0, "k": [192, 96]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 104, "ty": 0, "parent": 96, "ks": {"a": {"a": 0, "k": [-1, -10]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 22.5, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 40.5, "s": [100], "h": 1}, {"t": 90, "s": [100], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [81.31, 25.2], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 22.5, "s": [81.31, 25.2], "i": {"x": [1, 0.25], "y": [1, 1]}, "o": {"x": [0, 0.25], "y": [0, 0.1]}}, {"t": 40.5, "s": [81.31, 0], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 90, "s": [81.31, 0], "h": 1}]}}, "w": 47, "h": 32, "ip": 0, "op": 91, "st": 0, "refId": "102"}, {"ind": 116, "ty": 0, "parent": 96, "ks": {"a": {"a": 0, "k": [0, -10]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 18, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 36, "s": [100], "h": 1}, {"t": 90, "s": [100], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [0, 25.2], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 18, "s": [0, 25.2], "i": {"x": [1, 0.25], "y": [1, 1]}, "o": {"x": [0, 0.25], "y": [0, 0.1]}}, {"t": 36, "s": [0, 0], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 90, "s": [0, 0], "h": 1}]}}, "w": 73, "h": 32, "ip": 0, "op": 91, "st": 0, "refId": "114"}, {"ind": 96, "ty": 3, "parent": 95, "ks": {"p": {"a": 0, "k": [0, 2.8]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 95, "ty": 3, "parent": 3, "ks": {"p": {"a": 0, "k": [192, 40]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 128, "ty": 0, "parent": 3, "ks": {"a": {"a": 0, "k": [0, -17]}, "o": {"a": 0, "k": 40}, "p": {"a": 0, "k": [844, 40]}}, "w": 68, "h": 26, "ip": 0, "op": 91, "st": 0, "refId": "126"}, {"ind": 141, "ty": 0, "parent": 129, "ks": {"a": {"a": 0, "k": [60, 60]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 9, "s": [0], "i": {"x": 0.66, "y": 1}, "o": {"x": 0.33, "y": 0.82}}, {"t": 24.66, "s": [110], "i": {"x": 0.67, "y": 1}, "o": {"x": 0.33, "y": 0}}, {"t": 36, "s": [100], "h": 1}, {"t": 90, "s": [100], "h": 1}]}, "p": {"a": 0, "k": [60, 60]}, "s": {"a": 1, "k": [{"t": 0, "s": [50, 50], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 9, "s": [50, 50], "i": {"x": [0.66, 0.66], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0.82, 0.82]}}, {"t": 24.66, "s": [105, 105], "i": {"x": [0.67, 0.67], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0, 0]}}, {"t": 36, "s": [100, 100], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 90, "s": [100, 100], "h": 1}]}}, "w": 120, "h": 120, "ip": 0, "op": 91, "st": 0, "refId": "139"}, {"ind": 129, "ty": 3, "parent": 3, "ks": {"p": {"a": 0, "k": [36, 36]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 145, "ty": 0, "parent": 3, "ks": {"o": {"a": 0, "k": 90}}, "w": 960, "h": 192, "ip": 0, "op": 91, "st": 0, "refId": "143"}, {"ind": 3, "ty": 3, "ks": {"p": {"a": 0, "k": [4, 4]}}, "ip": 0, "op": 91, "st": 0}]}], "fr": 30, "h": 312, "ip": 0, "layers": [{"ind": 148, "ty": 0, "parent": 2, "ks": {"a": {"a": 0, "k": [484, 100]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 13.5, "s": [100], "h": 1}, {"t": 90, "s": [100], "h": 1}]}, "p": {"a": 0, "k": [540, 156]}, "s": {"a": 1, "k": [{"t": 0, "s": [60, 60], "i": {"x": [0.66, 0.66], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0.82, 0.82]}}, {"t": 20.89, "s": [104, 104], "i": {"x": [0.67, 0.67], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0, 0]}}, {"t": 36, "s": [100, 100], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 90, "s": [100, 100], "h": 1}]}}, "w": 964, "h": 196, "ip": 0, "op": 91, "st": 0, "refId": "146"}, {"ind": 2, "ty": 3, "parent": 1, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 1, "ty": 3, "ks": {}, "ip": 0, "op": 91, "st": 0}], "op": 90, "v": "5.7.4", "w": 1080}