'use client'

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { lottieManager } from '@/lib/lottie-manager'
import PerformanceMonitor from './PerformanceMonitor'

interface AnimationContextType {
  preloadAnimations: (keys: string[]) => Promise<void>
  clearCache: () => void
  getCacheStats: () => any
  isReady: boolean
}

const AnimationContext = createContext<AnimationContextType | null>(null)

interface AnimationProviderProps {
  children: ReactNode
  criticalAnimations?: string[]
  enablePerformanceMonitor?: boolean
  maxCacheSize?: number
  preloadStrategy?: 'immediate' | 'idle' | 'interaction'
}

export const AnimationProvider: React.FC<AnimationProviderProps> = ({
  children,
  criticalAnimations = ['notification'], // Default critical animations
  enablePerformanceMonitor = process.env.NODE_ENV === 'development',
  maxCacheSize = 10,
  preloadStrategy = 'idle'
}) => {
  const [isReady, setIsReady] = useState(false)
  const [hasPreloaded, setHasPreloaded] = useState(false)

  // Preload critical animations
  useEffect(() => {
    const preloadCritical = async () => {
      if (hasPreloaded || criticalAnimations.length === 0) return

      try {
        if (preloadStrategy === 'immediate') {
          await lottieManager.preloadCriticalAnimations(criticalAnimations)
          setHasPreloaded(true)
          setIsReady(true)
        } else if (preloadStrategy === 'idle') {
          // Use requestIdleCallback if available
          if ('requestIdleCallback' in window) {
            window.requestIdleCallback(async () => {
              await lottieManager.preloadCriticalAnimations(criticalAnimations)
              setHasPreloaded(true)
              setIsReady(true)
            })
          } else {
            // Fallback to setTimeout
            setTimeout(async () => {
              await lottieManager.preloadCriticalAnimations(criticalAnimations)
              setHasPreloaded(true)
              setIsReady(true)
            }, 100)
          }
        } else if (preloadStrategy === 'interaction') {
          // Wait for first user interaction
          const handleFirstInteraction = async () => {
            await lottieManager.preloadCriticalAnimations(criticalAnimations)
            setHasPreloaded(true)
            setIsReady(true)
            
            // Remove listeners after first interaction
            window.removeEventListener('click', handleFirstInteraction)
            window.removeEventListener('scroll', handleFirstInteraction)
            window.removeEventListener('keydown', handleFirstInteraction)
          }

          window.addEventListener('click', handleFirstInteraction, { once: true })
          window.addEventListener('scroll', handleFirstInteraction, { once: true })
          window.addEventListener('keydown', handleFirstInteraction, { once: true })

          // Fallback timeout
          setTimeout(() => {
            if (!hasPreloaded) {
              handleFirstInteraction()
            }
          }, 5000)

          return () => {
            window.removeEventListener('click', handleFirstInteraction)
            window.removeEventListener('scroll', handleFirstInteraction)
            window.removeEventListener('keydown', handleFirstInteraction)
          }
        }
      } catch (error) {
        console.error('Failed to preload critical animations:', error)
        setIsReady(true) // Still mark as ready to not block the app
      }
    }

    preloadCritical()
  }, [criticalAnimations, hasPreloaded, preloadStrategy])

  // Manage cache size
  useEffect(() => {
    const manageCacheInterval = setInterval(() => {
      lottieManager.manageCacheSize(maxCacheSize)
    }, 30000) // Check every 30 seconds

    return () => clearInterval(manageCacheInterval)
  }, [maxCacheSize])

  // Memory cleanup on unmount
  useEffect(() => {
    return () => {
      lottieManager.clearAll()
    }
  }, [])

  // Context value
  const contextValue: AnimationContextType = {
    preloadAnimations: async (keys: string[]) => {
      await lottieManager.preloadCriticalAnimations(keys)
    },
    clearCache: () => {
      lottieManager.clearAll()
    },
    getCacheStats: () => {
      return lottieManager.getCacheStats()
    },
    isReady
  }

  return (
    <AnimationContext.Provider value={contextValue}>
      {children}
      {enablePerformanceMonitor && (
        <PerformanceMonitor 
          enabled={true}
          position="bottom-right"
          showDetails={true}
        />
      )}
    </AnimationContext.Provider>
  )
}

// Hook to use animation context
export const useAnimationContext = () => {
  const context = useContext(AnimationContext)
  if (!context) {
    throw new Error('useAnimationContext must be used within an AnimationProvider')
  }
  return context
}

// HOC for components that need animation context
export const withAnimationContext = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const WrappedComponent = (props: P) => {
    const animationContext = useAnimationContext()
    return <Component {...props} animationContext={animationContext} />
  }

  WrappedComponent.displayName = `withAnimationContext(${Component.displayName || Component.name})`
  return WrappedComponent
}

// Performance-aware animation loader
export const useAnimationLoader = () => {
  const { isReady } = useAnimationContext()
  const [loadedAnimations, setLoadedAnimations] = useState<Set<string>>(new Set())
  const [loadingAnimations, setLoadingAnimations] = useState<Set<string>>(new Set())

  const loadAnimation = async (key: string, priority: 'high' | 'medium' | 'low' = 'medium') => {
    if (loadedAnimations.has(key) || loadingAnimations.has(key)) {
      return
    }

    setLoadingAnimations(prev => new Set([...prev, key]))

    try {
      await lottieManager.loadAnimation(key, {
        quality: priority === 'high' ? 'high' : priority === 'medium' ? 'medium' : 'low'
      })
      
      setLoadedAnimations(prev => new Set([...prev, key]))
    } catch (error) {
      console.error(`Failed to load animation: ${key}`, error)
    } finally {
      setLoadingAnimations(prev => {
        const newSet = new Set(prev)
        newSet.delete(key)
        return newSet
      })
    }
  }

  const preloadAnimations = async (keys: string[]) => {
    const promises = keys.map(key => loadAnimation(key, 'medium'))
    await Promise.all(promises)
  }

  return {
    loadAnimation,
    preloadAnimations,
    isLoaded: (key: string) => loadedAnimations.has(key),
    isLoading: (key: string) => loadingAnimations.has(key),
    isReady
  }
}

// Batch animation loader for performance
export const useBatchAnimationLoader = (batchSize: number = 3) => {
  const [queue, setQueue] = useState<string[]>([])
  const [processing, setProcessing] = useState(false)

  const addToQueue = (animationKeys: string[]) => {
    setQueue(prev => [...prev, ...animationKeys.filter(key => !prev.includes(key))])
  }

  const processBatch = async () => {
    if (processing || queue.length === 0) return

    setProcessing(true)
    const batch = queue.slice(0, batchSize)
    
    try {
      const promises = batch.map(key => 
        lottieManager.loadAnimation(key).catch(err => {
          console.warn(`Failed to load animation in batch: ${key}`, err)
          return null
        })
      )
      
      await Promise.all(promises)
      setQueue(prev => prev.slice(batchSize))
    } catch (error) {
      console.error('Batch loading failed:', error)
    } finally {
      setProcessing(false)
    }
  }

  useEffect(() => {
    if (queue.length > 0 && !processing) {
      // Process batch with a small delay to allow for batching
      const timeout = setTimeout(processBatch, 100)
      return () => clearTimeout(timeout)
    }
  }, [queue.length, processing])

  return {
    addToQueue,
    queueLength: queue.length,
    isProcessing: processing
  }
}

export default AnimationProvider
