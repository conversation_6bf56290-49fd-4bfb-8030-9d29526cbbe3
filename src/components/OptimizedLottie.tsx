'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react'
import Lot<PERSON> from 'lottie-react'
import { lottieManager, performanceMonitor } from '@/lib/lottie-manager'
import { prefersReducedMotion } from '@/lib/motion'

interface OptimizedLottieProps {
  animationKey: string
  className?: string
  style?: React.CSSProperties
  autoplay?: boolean
  loop?: boolean
  speed?: number
  quality?: 'low' | 'medium' | 'high'
  lazy?: boolean
  preload?: boolean
  onComplete?: () => void
  onLoopComplete?: () => void
  onError?: (error: Error) => void
  intersectionOptions?: IntersectionObserverInit
  fallback?: React.ReactNode
  id?: string
}

const OptimizedLottie: React.FC<OptimizedLottieProps> = ({
  animationKey,
  className = '',
  style = {},
  autoplay = true,
  loop = true,
  speed = 1,
  quality = 'medium',
  lazy = true,
  preload = false,
  onComplete,
  onLoopComplete,
  onError,
  intersectionOptions,
  fallback,
  id
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const lottieRef = useRef<any>(null)
  const [animationData, setAnimationData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [isVisible, setIsVisible] = useState(!lazy)
  const instanceId = useRef(id || `lottie-${animationKey}-${Math.random().toString(36).substr(2, 9)}`)

  // Load animation data
  const loadAnimation = useCallback(async () => {
    if (animationData || isLoading) return

    setIsLoading(true)
    setError(null)

    try {
      const data = await performanceMonitor.measureAnimationLoad(
        animationKey,
        () => lottieManager.loadAnimation(animationKey, {
          autoplay,
          loop,
          speed,
          quality,
          preload,
          lazy
        })
      )
      
      setAnimationData(data)
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to load animation')
      setError(error)
      onError?.(error)
    } finally {
      setIsLoading(false)
    }
  }, [animationKey, autoplay, loop, speed, quality, preload, lazy, onError, animationData, isLoading])

  // Setup intersection observer for lazy loading
  useEffect(() => {
    if (!lazy || isVisible || !containerRef.current) return

    const observer = lottieManager.createIntersectionObserver(
      instanceId.current,
      () => {
        setIsVisible(true)
      },
      intersectionOptions
    )

    observer.observe(containerRef.current)

    return () => {
      lottieManager.cleanup(instanceId.current)
    }
  }, [lazy, isVisible, intersectionOptions])

  // Load animation when visible
  useEffect(() => {
    if (isVisible && !animationData && !isLoading) {
      loadAnimation()
    }
  }, [isVisible, animationData, isLoading, loadAnimation])

  // Preload animation if requested
  useEffect(() => {
    if (preload && !lazy) {
      loadAnimation()
    }
  }, [preload, lazy, loadAnimation])

  // Register Lottie instance with manager
  useEffect(() => {
    if (lottieRef.current && animationData) {
      lottieManager.registerInstance(instanceId.current, lottieRef.current, {
        autoplay,
        loop,
        speed,
        quality
      })

      // Apply reduced motion preferences
      if (prefersReducedMotion()) {
        if (autoplay) {
          lottieRef.current.setSpeed(0.1) // Very slow
        } else {
          lottieRef.current.pause()
        }
      } else {
        lottieRef.current.setSpeed(speed)
      }
    }

    return () => {
      lottieManager.cleanup(instanceId.current)
    }
  }, [animationData, autoplay, loop, speed, quality])

  // Handle Lottie events
  const handleComplete = useCallback(() => {
    onComplete?.()
  }, [onComplete])

  const handleLoopComplete = useCallback(() => {
    onLoopComplete?.()
  }, [onLoopComplete])

  // Render loading state
  if (isLoading) {
    return (
      <div 
        ref={containerRef}
        className={`lottie-loading ${className}`}
        style={{
          ...style,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: style.height || '100px'
        }}
      >
        {fallback || (
          <div className="animate-pulse bg-gray-200 rounded" style={{
            width: '100%',
            height: '100%',
            minHeight: '60px'
          }} />
        )}
      </div>
    )
  }

  // Render error state
  if (error) {
    return (
      <div 
        ref={containerRef}
        className={`lottie-error ${className}`}
        style={{
          ...style,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#ef4444',
          fontSize: '14px'
        }}
      >
        Failed to load animation
      </div>
    )
  }

  // Render placeholder for lazy loading
  if (!isVisible) {
    return (
      <div 
        ref={containerRef}
        className={`lottie-placeholder ${className}`}
        style={{
          ...style,
          minHeight: style.height || '100px'
        }}
      />
    )
  }

  // Render Lottie animation
  if (!animationData) {
    return (
      <div 
        ref={containerRef}
        className={`lottie-loading ${className}`}
        style={style}
      />
    )
  }

  return (
    <div ref={containerRef} className={className} style={style}>
      <Lottie
        lottieRef={lottieRef}
        animationData={animationData}
        autoplay={autoplay && !prefersReducedMotion()}
        loop={loop}
        onComplete={handleComplete}
        onLoopComplete={handleLoopComplete}
        style={{ width: '100%', height: '100%' }}
      />
    </div>
  )
}

// Higher-order component for preloading animations
export const withPreloadedLottie = (animationKey: string, config?: Partial<OptimizedLottieProps>) => {
  return (WrappedComponent: React.ComponentType<any>) => {
    const PreloadedComponent = (props: any) => {
      useEffect(() => {
        // Preload during idle time
        lottieManager.preloadCriticalAnimations([animationKey])
      }, [])

      return <WrappedComponent {...props} />
    }

    PreloadedComponent.displayName = `withPreloadedLottie(${WrappedComponent.displayName || WrappedComponent.name})`
    return PreloadedComponent
  }
}

// Hook for managing multiple animations
export const useLottieManager = () => {
  const [cacheStats, setCacheStats] = useState(lottieManager.getCacheStats())

  const refreshStats = useCallback(() => {
    setCacheStats(lottieManager.getCacheStats())
  }, [])

  const clearCache = useCallback(() => {
    lottieManager.clearAll()
    refreshStats()
  }, [refreshStats])

  const preloadAnimations = useCallback(async (animationKeys: string[]) => {
    await lottieManager.preloadCriticalAnimations(animationKeys)
    refreshStats()
  }, [refreshStats])

  useEffect(() => {
    // Refresh stats periodically
    const interval = setInterval(refreshStats, 5000)
    return () => clearInterval(interval)
  }, [refreshStats])

  return {
    cacheStats,
    refreshStats,
    clearCache,
    preloadAnimations,
    getMetrics: lottieManager.getMetrics.bind(lottieManager)
  }
}

export default OptimizedLottie
