'use client'

import { useState, useEffect } from 'react'
import Navbar from '@/components/Navbar'
import AnimationProvider from '@/components/AnimationProvider'

export default function SiteLayout({ children }) {
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    // Function to handle scroll event
    const handleScroll = () => {
      // Check if page has scrolled more than 20px
      const scrolled = window.scrollY > 20
      setIsScrolled(scrolled)
      
      // Apply class to body element when scrolled
      if (scrolled) {
        document.body.classList.add('is-scrolled')
      } else {
        document.body.classList.remove('is-scrolled')
      }
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll)
    
    // Initial check in case page is loaded already scrolled
    handleScroll()

    // Clean up event listener on component unmount
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  return (
    <AnimationProvider
      criticalAnimations={['notification']}
      enablePerformanceMonitor={process.env.NODE_ENV === 'development'}
      preloadStrategy="idle"
      maxCacheSize={8}
    >
      {/* Skip to content link for accessibility */}
      <a href="#main-content" className="skip-to-content">
        Skip to main content
      </a>
      <Navbar isScrolled={isScrolled} />
      <main id="main-content">
        {children}
      </main>
    </AnimationProvider>
  )
}
