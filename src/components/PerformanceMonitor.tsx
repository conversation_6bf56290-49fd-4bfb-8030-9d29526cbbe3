'use client'

import React, { useEffect, useState } from 'react'
import { lottieManager, performanceMonitor } from '@/lib/lottie-manager'

interface PerformanceStats {
  totalAnimations: number
  cacheSize: number
  totalCacheSize: number
  loadTimes: Record<string, number>
  renderTimes: Record<string, number>
  memoryUsage?: number
}

interface PerformanceMonitorProps {
  enabled?: boolean
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  showDetails?: boolean
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  enabled = process.env.NODE_ENV === 'development',
  position = 'bottom-right',
  showDetails = false
}) => {
  const cacheStats = lottieManager.getCacheStats()
  const getMetrics = () => lottieManager.getMetrics()
  const [stats, setStats] = useState<PerformanceStats>({
    totalAnimations: 0,
    cacheSize: 0,
    totalCacheSize: 0,
    loadTimes: {},
    renderTimes: {}
  })
  const [isExpanded, setIsExpanded] = useState(false)
  const [memoryInfo, setMemoryInfo] = useState<any>(null)

  // Update stats periodically
  useEffect(() => {
    if (!enabled) return

    const updateStats = () => {
      const currentCacheStats = lottieManager.getCacheStats()
      const metrics = getMetrics() as Map<string, any>
      const loadTimes: Record<string, number> = {}
      const renderTimes: Record<string, number> = {}

      if (metrics instanceof Map) {
        metrics.forEach((metric, key) => {
          loadTimes[key] = metric.loadTime || 0
          renderTimes[key] = metric.renderTime || 0
        })
      }

      setStats({
        totalAnimations: metrics instanceof Map ? metrics.size : 0,
        cacheSize: currentCacheStats.size,
        totalCacheSize: currentCacheStats.totalSize,
        loadTimes,
        renderTimes,
        memoryUsage: (performance as any).memory?.usedJSHeapSize
      })

      // Get memory info if available
      if ('memory' in performance) {
        setMemoryInfo((performance as any).memory)
      }
    }

    updateStats()
    const interval = setInterval(updateStats, 2000)

    return () => clearInterval(interval)
  }, [enabled])

  // Performance observer for monitoring
  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return

    // Monitor long tasks
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.duration > 50) { // Tasks longer than 50ms
            console.warn(`🐌 Long task detected: ${entry.duration.toFixed(2)}ms`)
          }
        })
      })

      try {
        observer.observe({ entryTypes: ['longtask'] })
      } catch (e) {
        // Longtask API not supported
      }

      return () => observer.disconnect()
    }
  }, [enabled])

  if (!enabled) return null

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getAverageLoadTime = () => {
    const times = Object.values(stats.loadTimes)
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0
  }

  return (
    <div 
      className={`fixed ${positionClasses[position]} z-50 bg-black/80 text-white text-xs rounded-lg p-3 font-mono backdrop-blur-sm border border-white/20`}
      style={{ minWidth: '200px' }}
    >
      <div 
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <span className="font-semibold">🎬 Lottie Performance</span>
        <span className="text-xs">{isExpanded ? '▼' : '▶'}</span>
      </div>

      <div className="mt-2 space-y-1">
        <div className="flex justify-between">
          <span>Animations:</span>
          <span className="text-green-400">{stats.totalAnimations}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Cache:</span>
          <span className="text-blue-400">{stats.cacheSize} items</span>
        </div>
        
        <div className="flex justify-between">
          <span>Cache Size:</span>
          <span className="text-yellow-400">{formatBytes(stats.totalCacheSize)}</span>
        </div>

        <div className="flex justify-between">
          <span>Avg Load:</span>
          <span className="text-purple-400">{getAverageLoadTime().toFixed(1)}ms</span>
        </div>

        {memoryInfo && (
          <div className="flex justify-between">
            <span>Memory:</span>
            <span className="text-red-400">{formatBytes(memoryInfo.usedJSHeapSize)}</span>
          </div>
        )}
      </div>

      {isExpanded && showDetails && (
        <div className="mt-3 pt-3 border-t border-white/20">
          <div className="text-xs font-semibold mb-2">Load Times:</div>
          {Object.entries(stats.loadTimes).map(([key, time]) => (
            <div key={key} className="flex justify-between text-xs">
              <span className="truncate max-w-24">{key}:</span>
              <span className={time > 100 ? 'text-red-400' : 'text-green-400'}>
                {time.toFixed(1)}ms
              </span>
            </div>
          ))}

          {memoryInfo && (
            <div className="mt-3 pt-3 border-t border-white/20">
              <div className="text-xs font-semibold mb-2">Memory Info:</div>
              <div className="flex justify-between text-xs">
                <span>Used:</span>
                <span>{formatBytes(memoryInfo.usedJSHeapSize)}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Total:</span>
                <span>{formatBytes(memoryInfo.totalJSHeapSize)}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Limit:</span>
                <span>{formatBytes(memoryInfo.jsHeapSizeLimit)}</span>
              </div>
            </div>
          )}
        </div>
      )}

      <div className="mt-2 pt-2 border-t border-white/20">
        <button
          onClick={() => {
            performanceMonitor.logCacheStats()
            console.log('🎬 Animation Metrics:', getMetrics())
          }}
          className="text-xs bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded transition-colors"
        >
          Log Stats
        </button>
      </div>
    </div>
  )
}

// Hook for performance monitoring
export const usePerformanceMonitoring = () => {
  const [metrics, setMetrics] = useState<any>({})

  useEffect(() => {
    // Monitor Core Web Vitals
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          const value = (entry as any).value || entry.duration || 0
          console.log(`📊 ${entry.name}: ${value}`)
          setMetrics((prev: any) => ({
            ...prev,
            [entry.name]: value
          }))
        })
      })

      try {
        observer.observe({ entryTypes: ['measure', 'navigation'] })
      } catch (e) {
        // Not supported
      }

      return () => observer.disconnect()
    }
  }, [])

  const measurePerformance = (name: string, fn: () => void) => {
    const start = performance.now()
    fn()
    const end = performance.now()
    const duration = end - start
    
    console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`)
    setMetrics((prev: any) => ({
      ...prev,
      [name]: duration
    }))
    
    return duration
  }

  return { metrics, measurePerformance }
}

export default PerformanceMonitor
